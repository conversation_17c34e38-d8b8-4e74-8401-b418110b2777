[package]
name = "data-parser"
version.workspace = true
edition.workspace = true

[dependencies]
shared = { path = "../shared" }

# Anchor framework
anchor-lang.workspace = true

async-trait.workspace = true
chrono.workspace = true
hex.workspace = true
solana-sdk.workspace = true
solana-transaction-status.workspace = true
serde.workspace = true
serde_json.workspace = true
thiserror.workspace = true
tracing.workspace = true
yellowstone-grpc-proto.workspace = true

[dev-dependencies]
tokio = { workspace = true, features = ["test-util"] }
