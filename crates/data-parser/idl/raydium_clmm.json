{"address": "CAMMCzo5YL8w4VFF8KVHrK22GGUsp5VTaW7grrKgrWqK", "metadata": {"name": "amm_v3", "version": "0.1.0", "spec": "0.1.0", "description": "Anchor client and source for Raydium concentrated liquidity AMM"}, "instructions": [{"name": "close_position", "docs": ["Close the user's position and NFT account. If the NFT mint belongs to token2022, it will also be closed and the funds returned to the NFT owner.", "", "# Arguments", "", "* `ctx` - The context of accounts", ""], "discriminator": [123, 134, 81, 0, 49, 68, 98, 98], "accounts": [{"name": "nft_owner", "docs": ["The position nft owner"], "writable": true, "signer": true}, {"name": "position_nft_mint", "docs": ["Mint address bound to the personal position."], "writable": true}, {"name": "position_nft_account", "docs": ["User token account where position NFT be minted to"], "writable": true}, {"name": "personal_position", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [112, 111, 115, 105, 116, 105, 111, 110]}, {"kind": "account", "path": "position_nft_mint"}]}}, {"name": "system_program", "docs": ["System program to close the position state account"], "address": "11111111111111111111111111111111"}, {"name": "token_program", "docs": ["Token/Token2022 program to close token/mint account"]}], "args": []}, {"name": "collect_fund_fee", "docs": ["Collect the fund fee accrued to the pool", "", "# Arguments", "", "* `ctx` - The context of accounts", "* `amount_0_requested` - The maximum amount of token_0 to send", "* `amount_1_requested` - The maximum amount of token_1 to send", ""], "discriminator": [167, 138, 78, 149, 223, 194, 6, 126], "accounts": [{"name": "owner", "docs": ["Only admin or fund_owner can collect fee now"], "signer": true}, {"name": "pool_state", "docs": ["Pool state stores accumulated protocol fee amount"], "writable": true}, {"name": "amm_config", "docs": ["Amm config account stores fund_owner"]}, {"name": "token_vault_0", "docs": ["The address that holds pool tokens for token_0"], "writable": true}, {"name": "token_vault_1", "docs": ["The address that holds pool tokens for token_1"], "writable": true}, {"name": "vault_0_mint", "docs": ["The mint of token vault 0"]}, {"name": "vault_1_mint", "docs": ["The mint of token vault 1"]}, {"name": "recipient_token_account_0", "docs": ["The address that receives the collected token_0 protocol fees"], "writable": true}, {"name": "recipient_token_account_1", "docs": ["The address that receives the collected token_1 protocol fees"], "writable": true}, {"name": "token_program", "docs": ["The SPL program to perform token transfers"], "address": "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA"}, {"name": "token_program_2022", "docs": ["The SPL program 2022 to perform token transfers"], "address": "TokenzQdBNbLqP5VEhdkAS6EPFLC1PHnBqCXEpPxuEb"}], "args": [{"name": "amount_0_requested", "type": "u64"}, {"name": "amount_1_requested", "type": "u64"}]}, {"name": "collect_protocol_fee", "docs": ["Collect the protocol fee accrued to the pool", "", "# Arguments", "", "* `ctx` - The context of accounts", "* `amount_0_requested` - The maximum amount of token_0 to send", "* `amount_1_requested` - The maximum amount of token_1 to send", ""], "discriminator": [136, 136, 252, 221, 194, 66, 126, 89], "accounts": [{"name": "owner", "docs": ["Only admin or config owner can collect fee now"], "signer": true}, {"name": "pool_state", "docs": ["Pool state stores accumulated protocol fee amount"], "writable": true}, {"name": "amm_config", "docs": ["Amm config account stores owner"]}, {"name": "token_vault_0", "docs": ["The address that holds pool tokens for token_0"], "writable": true}, {"name": "token_vault_1", "docs": ["The address that holds pool tokens for token_1"], "writable": true}, {"name": "vault_0_mint", "docs": ["The mint of token vault 0"]}, {"name": "vault_1_mint", "docs": ["The mint of token vault 1"]}, {"name": "recipient_token_account_0", "docs": ["The address that receives the collected token_0 protocol fees"], "writable": true}, {"name": "recipient_token_account_1", "docs": ["The address that receives the collected token_1 protocol fees"], "writable": true}, {"name": "token_program", "docs": ["The SPL program to perform token transfers"], "address": "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA"}, {"name": "token_program_2022", "docs": ["The SPL program 2022 to perform token transfers"], "address": "TokenzQdBNbLqP5VEhdkAS6EPFLC1PHnBqCXEpPxuEb"}], "args": [{"name": "amount_0_requested", "type": "u64"}, {"name": "amount_1_requested", "type": "u64"}]}, {"name": "collect_remaining_rewards", "docs": ["Collect remaining reward token for reward founder", "", "# Arguments", "", "* `ctx`- The context of accounts", "* `reward_index` - the index to reward info, it must be smaller than 3", ""], "discriminator": [18, 237, 166, 197, 34, 16, 213, 144], "accounts": [{"name": "reward_funder", "docs": ["The founder who init reward info previously"], "signer": true}, {"name": "funder_token_account", "docs": ["The funder's reward token account"], "writable": true}, {"name": "pool_state", "docs": ["Set reward for this pool"], "writable": true}, {"name": "reward_token_vault", "docs": ["Reward vault transfer remaining token to founder token account"]}, {"name": "reward_vault_mint", "docs": ["The mint of reward token vault"]}, {"name": "token_program", "address": "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA"}, {"name": "token_program_2022", "docs": ["Token program 2022"], "address": "TokenzQdBNbLqP5VEhdkAS6EPFLC1PHnBqCXEpPxuEb"}, {"name": "memo_program", "docs": ["memo program"], "address": "MemoSq4gqABAXKb96qnH8TysNcWxMyWCqXgDLGmfcHr"}], "args": [{"name": "reward_index", "type": "u8"}]}, {"name": "create_amm_config", "docs": ["# Arguments", "", "* `ctx`- The accounts needed by instruction.", "* `index` - The index of amm config, there may be multiple config.", "* `tick_spacing` - The tickspacing binding with config, cannot be changed.", "* `trade_fee_rate` - Trade fee rate, can be changed.", "* `protocol_fee_rate` - The rate of protocol fee within trade fee.", "* `fund_fee_rate` - The rate of fund fee within trade fee.", ""], "discriminator": [137, 52, 237, 212, 215, 117, 108, 104], "accounts": [{"name": "owner", "docs": ["Address to be set as protocol owner."], "writable": true, "signer": true, "address": "GThUX1Atko4tqhN2NaiTazWSeFWMuiUvfFnyJyUghFMJ"}, {"name": "amm_config", "docs": ["Initialize config state account to store protocol owner address and fee rates."], "writable": true, "pda": {"seeds": [{"kind": "const", "value": [97, 109, 109, 95, 99, 111, 110, 102, 105, 103]}, {"kind": "arg", "path": "index"}]}}, {"name": "system_program", "address": "11111111111111111111111111111111"}], "args": [{"name": "index", "type": "u16"}, {"name": "tick_spacing", "type": "u16"}, {"name": "trade_fee_rate", "type": "u32"}, {"name": "protocol_fee_rate", "type": "u32"}, {"name": "fund_fee_rate", "type": "u32"}]}, {"name": "create_operation_account", "docs": ["Creates an operation account for the program", "", "# Arguments", "", "* `ctx`- The context of accounts", ""], "discriminator": [63, 87, 148, 33, 109, 35, 8, 104], "accounts": [{"name": "owner", "docs": ["Address to be set as operation account owner."], "writable": true, "signer": true, "address": "GThUX1Atko4tqhN2NaiTazWSeFWMuiUvfFnyJyUghFMJ"}, {"name": "operation_state", "docs": ["Initialize operation state account to store operation owner address and white list mint."], "writable": true, "pda": {"seeds": [{"kind": "const", "value": [111, 112, 101, 114, 97, 116, 105, 111, 110]}]}}, {"name": "system_program", "address": "11111111111111111111111111111111"}], "args": []}, {"name": "create_pool", "docs": ["Creates a pool for the given token pair and the initial price", "", "# Arguments", "", "* `ctx`- The context of accounts", "* `sqrt_price_x64` - the initial sqrt price (amount_token_1 / amount_token_0) of the pool as a Q64.64", "Note: The open_time must be smaller than the current block_timestamp on chain."], "discriminator": [233, 146, 209, 142, 207, 104, 64, 188], "accounts": [{"name": "pool_creator", "docs": ["Address paying to create the pool. Can be anyone"], "writable": true, "signer": true}, {"name": "amm_config", "docs": ["Which config the pool belongs to."]}, {"name": "pool_state", "docs": ["Initialize an account to store the pool state"], "writable": true, "pda": {"seeds": [{"kind": "const", "value": [112, 111, 111, 108]}, {"kind": "account", "path": "amm_config"}, {"kind": "account", "path": "token_mint_0"}, {"kind": "account", "path": "token_mint_1"}]}}, {"name": "token_mint_0", "docs": ["Token_0 mint, the key must be smaller then token_1 mint."]}, {"name": "token_mint_1", "docs": ["Token_1 mint"]}, {"name": "token_vault_0", "docs": ["Token_0 vault for the pool"], "writable": true, "pda": {"seeds": [{"kind": "const", "value": [112, 111, 111, 108, 95, 118, 97, 117, 108, 116]}, {"kind": "account", "path": "pool_state"}, {"kind": "account", "path": "token_mint_0"}]}}, {"name": "token_vault_1", "docs": ["Token_1 vault for the pool"], "writable": true, "pda": {"seeds": [{"kind": "const", "value": [112, 111, 111, 108, 95, 118, 97, 117, 108, 116]}, {"kind": "account", "path": "pool_state"}, {"kind": "account", "path": "token_mint_1"}]}}, {"name": "observation_state", "docs": ["Initialize an account to store oracle observations"], "writable": true, "pda": {"seeds": [{"kind": "const", "value": [111, 98, 115, 101, 114, 118, 97, 116, 105, 111, 110]}, {"kind": "account", "path": "pool_state"}]}}, {"name": "tick_array_bitmap", "docs": ["Initialize an account to store if a tick array is initialized."], "writable": true, "pda": {"seeds": [{"kind": "const", "value": [112, 111, 111, 108, 95, 116, 105, 99, 107, 95, 97, 114, 114, 97, 121, 95, 98, 105, 116, 109, 97, 112, 95, 101, 120, 116, 101, 110, 115, 105, 111, 110]}, {"kind": "account", "path": "pool_state"}]}}, {"name": "token_program_0", "docs": ["Spl token program or token program 2022"]}, {"name": "token_program_1", "docs": ["Spl token program or token program 2022"]}, {"name": "system_program", "docs": ["To create a new program account"], "address": "11111111111111111111111111111111"}, {"name": "rent", "docs": ["Sysvar for program account"], "address": "SysvarRent111111111111111111111111111111111"}], "args": [{"name": "sqrt_price_x64", "type": "u128"}, {"name": "open_time", "type": "u64"}]}, {"name": "create_support_mint_associated", "docs": ["Create support token22 mint account which can create pool and send rewards with ignoring the not support extensions."], "discriminator": [17, 251, 65, 92, 136, 242, 14, 169], "accounts": [{"name": "owner", "docs": ["Address to be set as protocol owner."], "writable": true, "signer": true, "address": "GThUX1Atko4tqhN2NaiTazWSeFWMuiUvfFnyJyUghFMJ"}, {"name": "token_mint", "docs": ["Support token mint"]}, {"name": "support_mint_associated", "docs": ["Initialize support mint state account to store support mint address and bump."], "writable": true, "pda": {"seeds": [{"kind": "const", "value": [115, 117, 112, 112, 111, 114, 116, 95, 109, 105, 110, 116]}, {"kind": "account", "path": "token_mint"}]}}, {"name": "system_program", "address": "11111111111111111111111111111111"}], "args": []}, {"name": "decrease_liquidity", "docs": ["#[deprecated(note = \"Use `decrease_liquidity_v2` instead.\")]", "Decreases liquidity for an existing position", "", "# Arguments", "", "* `ctx` -  The context of accounts", "* `liquidity` - The amount by which liquidity will be decreased", "* `amount_0_min` - The minimum amount of token_0 that should be accounted for the burned liquidity", "* `amount_1_min` - The minimum amount of token_1 that should be accounted for the burned liquidity", ""], "discriminator": [160, 38, 208, 111, 104, 91, 44, 1], "accounts": [{"name": "nft_owner", "docs": ["The position owner or delegated authority"], "signer": true}, {"name": "nft_account", "docs": ["The token account for the tokenized position"]}, {"name": "personal_position", "docs": ["Decrease liquidity for this position"], "writable": true}, {"name": "pool_state", "writable": true}, {"name": "protocol_position", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [112, 111, 115, 105, 116, 105, 111, 110]}, {"kind": "account", "path": "pool_state"}, {"kind": "account", "path": "personal_position.tick_lower_index", "account": "PersonalPositionState"}, {"kind": "account", "path": "personal_position.tick_upper_index", "account": "PersonalPositionState"}]}}, {"name": "token_vault_0", "docs": ["Token_0 vault"], "writable": true}, {"name": "token_vault_1", "docs": ["Token_1 vault"], "writable": true}, {"name": "tick_array_lower", "docs": ["Stores init state for the lower tick"], "writable": true}, {"name": "tick_array_upper", "docs": ["Stores init state for the upper tick"], "writable": true}, {"name": "recipient_token_account_0", "docs": ["The destination token account for receive amount_0"], "writable": true}, {"name": "recipient_token_account_1", "docs": ["The destination token account for receive amount_1"], "writable": true}, {"name": "token_program", "docs": ["SPL program to transfer out tokens"], "address": "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA"}], "args": [{"name": "liquidity", "type": "u128"}, {"name": "amount_0_min", "type": "u64"}, {"name": "amount_1_min", "type": "u64"}]}, {"name": "decrease_liquidity_v2", "docs": ["Decreases liquidity for an existing position, support Token2022", "", "# Arguments", "", "* `ctx` -  The context of accounts", "* `liquidity` - The amount by which liquidity will be decreased", "* `amount_0_min` - The minimum amount of token_0 that should be accounted for the burned liquidity", "* `amount_1_min` - The minimum amount of token_1 that should be accounted for the burned liquidity", ""], "discriminator": [58, 127, 188, 62, 79, 82, 196, 96], "accounts": [{"name": "nft_owner", "docs": ["The position owner or delegated authority"], "signer": true}, {"name": "nft_account", "docs": ["The token account for the tokenized position"]}, {"name": "personal_position", "docs": ["Decrease liquidity for this position"], "writable": true}, {"name": "pool_state", "writable": true}, {"name": "protocol_position", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [112, 111, 115, 105, 116, 105, 111, 110]}, {"kind": "account", "path": "pool_state"}, {"kind": "account", "path": "personal_position.tick_lower_index", "account": "PersonalPositionState"}, {"kind": "account", "path": "personal_position.tick_upper_index", "account": "PersonalPositionState"}]}}, {"name": "token_vault_0", "docs": ["Token_0 vault"], "writable": true}, {"name": "token_vault_1", "docs": ["Token_1 vault"], "writable": true}, {"name": "tick_array_lower", "docs": ["Stores init state for the lower tick"], "writable": true}, {"name": "tick_array_upper", "docs": ["Stores init state for the upper tick"], "writable": true}, {"name": "recipient_token_account_0", "docs": ["The destination token account for receive amount_0"], "writable": true}, {"name": "recipient_token_account_1", "docs": ["The destination token account for receive amount_1"], "writable": true}, {"name": "token_program", "docs": ["SPL program to transfer out tokens"], "address": "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA"}, {"name": "token_program_2022", "docs": ["Token program 2022"], "address": "TokenzQdBNbLqP5VEhdkAS6EPFLC1PHnBqCXEpPxuEb"}, {"name": "memo_program", "docs": ["memo program"], "address": "MemoSq4gqABAXKb96qnH8TysNcWxMyWCqXgDLGmfcHr"}, {"name": "vault_0_mint", "docs": ["The mint of token vault 0"]}, {"name": "vault_1_mint", "docs": ["The mint of token vault 1"]}], "args": [{"name": "liquidity", "type": "u128"}, {"name": "amount_0_min", "type": "u64"}, {"name": "amount_1_min", "type": "u64"}]}, {"name": "increase_liquidity", "docs": ["#[deprecated(note = \"Use `increase_liquidity_v2` instead.\")]", "Increases liquidity for an existing position, with amount paid by `payer`", "", "# Arguments", "", "* `ctx` - The context of accounts", "* `liquidity` - The desired liquidity to be added, can't be zero", "* `amount_0_max` - The max amount of token_0 to spend, which serves as a slippage check", "* `amount_1_max` - The max amount of token_1 to spend, which serves as a slippage check", ""], "discriminator": [46, 156, 243, 118, 13, 205, 251, 178], "accounts": [{"name": "nft_owner", "docs": ["Pays to mint the position"], "signer": true}, {"name": "nft_account", "docs": ["The token account for nft"]}, {"name": "pool_state", "writable": true}, {"name": "protocol_position", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [112, 111, 115, 105, 116, 105, 111, 110]}, {"kind": "account", "path": "pool_state"}, {"kind": "account", "path": "personal_position.tick_lower_index", "account": "PersonalPositionState"}, {"kind": "account", "path": "personal_position.tick_upper_index", "account": "PersonalPositionState"}]}}, {"name": "personal_position", "docs": ["Increase liquidity for this position"], "writable": true}, {"name": "tick_array_lower", "docs": ["Stores init state for the lower tick"], "writable": true}, {"name": "tick_array_upper", "docs": ["Stores init state for the upper tick"], "writable": true}, {"name": "token_account_0", "docs": ["The payer's token account for token_0"], "writable": true}, {"name": "token_account_1", "docs": ["The token account spending token_1 to mint the position"], "writable": true}, {"name": "token_vault_0", "docs": ["The address that holds pool tokens for token_0"], "writable": true}, {"name": "token_vault_1", "docs": ["The address that holds pool tokens for token_1"], "writable": true}, {"name": "token_program", "docs": ["Program to create mint account and mint tokens"], "address": "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA"}], "args": [{"name": "liquidity", "type": "u128"}, {"name": "amount_0_max", "type": "u64"}, {"name": "amount_1_max", "type": "u64"}]}, {"name": "increase_liquidity_v2", "docs": ["Increases liquidity for an existing position, with amount paid by `payer`, support Token2022", "", "# Arguments", "", "* `ctx` - The context of accounts", "* `liquidity` - The desired liquidity to be added, if zero, calculate liquidity base amount_0 or amount_1 according base_flag", "* `amount_0_max` - The max amount of token_0 to spend, which serves as a slippage check", "* `amount_1_max` - The max amount of token_1 to spend, which serves as a slippage check", "* `base_flag` - must be specified if liquidity is zero, true: calculate liquidity base amount_0_max otherwise base amount_1_max", ""], "discriminator": [133, 29, 89, 223, 69, 238, 176, 10], "accounts": [{"name": "nft_owner", "docs": ["Pays to mint the position"], "signer": true}, {"name": "nft_account", "docs": ["The token account for nft"]}, {"name": "pool_state", "writable": true}, {"name": "protocol_position", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [112, 111, 115, 105, 116, 105, 111, 110]}, {"kind": "account", "path": "pool_state"}, {"kind": "account", "path": "personal_position.tick_lower_index", "account": "PersonalPositionState"}, {"kind": "account", "path": "personal_position.tick_upper_index", "account": "PersonalPositionState"}]}}, {"name": "personal_position", "docs": ["Increase liquidity for this position"], "writable": true}, {"name": "tick_array_lower", "docs": ["Stores init state for the lower tick"], "writable": true}, {"name": "tick_array_upper", "docs": ["Stores init state for the upper tick"], "writable": true}, {"name": "token_account_0", "docs": ["The payer's token account for token_0"], "writable": true}, {"name": "token_account_1", "docs": ["The token account spending token_1 to mint the position"], "writable": true}, {"name": "token_vault_0", "docs": ["The address that holds pool tokens for token_0"], "writable": true}, {"name": "token_vault_1", "docs": ["The address that holds pool tokens for token_1"], "writable": true}, {"name": "token_program", "docs": ["Program to create mint account and mint tokens"], "address": "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA"}, {"name": "token_program_2022", "docs": ["Token program 2022"], "address": "TokenzQdBNbLqP5VEhdkAS6EPFLC1PHnBqCXEpPxuEb"}, {"name": "vault_0_mint", "docs": ["The mint of token vault 0"]}, {"name": "vault_1_mint", "docs": ["The mint of token vault 1"]}], "args": [{"name": "liquidity", "type": "u128"}, {"name": "amount_0_max", "type": "u64"}, {"name": "amount_1_max", "type": "u64"}, {"name": "base_flag", "type": {"option": "bool"}}]}, {"name": "initialize_reward", "docs": ["Initialize a reward info for a given pool and reward index", "", "# Arguments", "", "* `ctx`- The context of accounts", "* `reward_index` - the index to reward info", "* `open_time` - reward open timestamp", "* `end_time` - reward end timestamp", "* `emissions_per_second_x64` - Token reward per second are earned per unit of liquidity.", ""], "discriminator": [95, 135, 192, 196, 242, 129, 230, 68], "accounts": [{"name": "reward_funder", "docs": ["The founder deposit reward token to vault"], "writable": true, "signer": true}, {"name": "funder_token_account", "writable": true}, {"name": "amm_config", "docs": ["For check the reward_funder authority"]}, {"name": "pool_state", "docs": ["Set reward for this pool"], "writable": true}, {"name": "operation_state", "docs": ["load info from the account to judge reward permission"], "pda": {"seeds": [{"kind": "const", "value": [111, 112, 101, 114, 97, 116, 105, 111, 110]}]}}, {"name": "reward_token_mint", "docs": ["Reward mint"]}, {"name": "reward_token_vault", "docs": ["A pda, reward vault"], "writable": true, "pda": {"seeds": [{"kind": "const", "value": [112, 111, 111, 108, 95, 114, 101, 119, 97, 114, 100, 95, 118, 97, 117, 108, 116]}, {"kind": "account", "path": "pool_state"}, {"kind": "account", "path": "reward_token_mint"}]}}, {"name": "reward_token_program"}, {"name": "system_program", "address": "11111111111111111111111111111111"}, {"name": "rent", "address": "SysvarRent111111111111111111111111111111111"}], "args": [{"name": "param", "type": {"defined": {"name": "InitializeRewardParam"}}}]}, {"name": "open_position", "docs": ["#[deprecated(note = \"Use `open_position_with_token22_nft` instead.\")]", "Creates a new position wrapped in a NFT", "", "# Arguments", "", "* `ctx` - The context of accounts", "* `tick_lower_index` - The low boundary of market", "* `tick_upper_index` - The upper boundary of market", "* `tick_array_lower_start_index` - The start index of tick array which include tick low", "* `tick_array_upper_start_index` - The start index of tick array which include tick upper", "* `liquidity` - The liquidity to be added", "* `amount_0_max` - The max amount of token_0 to spend, which serves as a slippage check", "* `amount_1_max` - The max amount of token_1 to spend, which serves as a slippage check", ""], "discriminator": [135, 128, 47, 77, 15, 152, 240, 49], "accounts": [{"name": "payer", "docs": ["Pays to mint the position"], "writable": true, "signer": true}, {"name": "position_nft_owner"}, {"name": "position_nft_mint", "docs": ["Unique token mint address"], "writable": true, "signer": true}, {"name": "position_nft_account", "docs": ["Token account where position NFT will be minted", "This account created in the contract by cpi to avoid large stack variables"], "writable": true, "pda": {"seeds": [{"kind": "account", "path": "position_nft_owner"}, {"kind": "const", "value": [6, 221, 246, 225, 215, 101, 161, 147, 217, 203, 225, 70, 206, 235, 121, 172, 28, 180, 133, 237, 95, 91, 55, 145, 58, 140, 245, 133, 126, 255, 0, 169]}, {"kind": "account", "path": "position_nft_mint"}], "program": {"kind": "const", "value": [140, 151, 37, 143, 78, 36, 137, 241, 187, 61, 16, 41, 20, 142, 13, 131, 11, 90, 19, 153, 218, 255, 16, 132, 4, 142, 123, 216, 219, 233, 248, 89]}}}, {"name": "metadata_account", "docs": ["To store metaplex metadata"], "writable": true}, {"name": "pool_state", "docs": ["Add liquidity for this pool"], "writable": true}, {"name": "protocol_position", "docs": ["Store the information of market marking in range"], "writable": true, "pda": {"seeds": [{"kind": "const", "value": [112, 111, 115, 105, 116, 105, 111, 110]}, {"kind": "account", "path": "pool_state"}, {"kind": "arg", "path": "tick_lower_index"}, {"kind": "arg", "path": "tick_upper_index"}]}}, {"name": "tick_array_lower", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [116, 105, 99, 107, 95, 97, 114, 114, 97, 121]}, {"kind": "account", "path": "pool_state"}, {"kind": "arg", "path": "tick_array_lower_start_index"}]}}, {"name": "tick_array_upper", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [116, 105, 99, 107, 95, 97, 114, 114, 97, 121]}, {"kind": "account", "path": "pool_state"}, {"kind": "arg", "path": "tick_array_upper_start_index"}]}}, {"name": "personal_position", "docs": ["personal position state"], "writable": true, "pda": {"seeds": [{"kind": "const", "value": [112, 111, 115, 105, 116, 105, 111, 110]}, {"kind": "account", "path": "position_nft_mint"}]}}, {"name": "token_account_0", "docs": ["The token_0 account deposit token to the pool"], "writable": true}, {"name": "token_account_1", "docs": ["The token_1 account deposit token to the pool"], "writable": true}, {"name": "token_vault_0", "docs": ["The address that holds pool tokens for token_0"], "writable": true}, {"name": "token_vault_1", "docs": ["The address that holds pool tokens for token_1"], "writable": true}, {"name": "rent", "docs": ["Sysvar for token mint and ATA creation"], "address": "SysvarRent111111111111111111111111111111111"}, {"name": "system_program", "docs": ["Program to create the position manager state account"], "address": "11111111111111111111111111111111"}, {"name": "token_program", "docs": ["Program to create mint account and mint tokens"], "address": "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA"}, {"name": "associated_token_program", "docs": ["Program to create an ATA for receiving position NFT"], "address": "ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL"}, {"name": "metadata_program", "docs": ["Program to create NFT metadata"], "address": "metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s"}], "args": [{"name": "tick_lower_index", "type": "i32"}, {"name": "tick_upper_index", "type": "i32"}, {"name": "tick_array_lower_start_index", "type": "i32"}, {"name": "tick_array_upper_start_index", "type": "i32"}, {"name": "liquidity", "type": "u128"}, {"name": "amount_0_max", "type": "u64"}, {"name": "amount_1_max", "type": "u64"}]}, {"name": "open_position_v2", "docs": ["#[deprecated(note = \"Use `open_position_with_token22_nft` instead.\")]", "Creates a new position wrapped in a NFT, support Token2022", "", "# Arguments", "", "* `ctx` - The context of accounts", "* `tick_lower_index` - The low boundary of market", "* `tick_upper_index` - The upper boundary of market", "* `tick_array_lower_start_index` - The start index of tick array which include tick low", "* `tick_array_upper_start_index` - The start index of tick array which include tick upper", "* `liquidity` - The liquidity to be added, if zero, and the base_flag is specified, calculate liquidity base amount_0_max or amount_1_max according base_flag, otherwise open position with zero liquidity", "* `amount_0_max` - The max amount of token_0 to spend, which serves as a slippage check", "* `amount_1_max` - The max amount of token_1 to spend, which serves as a slippage check", "* `with_metadata` - The flag indicating whether to create NFT mint metadata", "* `base_flag` - if the liquidity specified as zero, true: calculate liquidity base amount_0_max otherwise base amount_1_max", ""], "discriminator": [77, 184, 74, 214, 112, 86, 241, 199], "accounts": [{"name": "payer", "docs": ["Pays to mint the position"], "writable": true, "signer": true}, {"name": "position_nft_owner"}, {"name": "position_nft_mint", "docs": ["Unique token mint address"], "writable": true, "signer": true}, {"name": "position_nft_account", "docs": ["Token account where position NFT will be minted"], "writable": true, "pda": {"seeds": [{"kind": "account", "path": "position_nft_owner"}, {"kind": "const", "value": [6, 221, 246, 225, 215, 101, 161, 147, 217, 203, 225, 70, 206, 235, 121, 172, 28, 180, 133, 237, 95, 91, 55, 145, 58, 140, 245, 133, 126, 255, 0, 169]}, {"kind": "account", "path": "position_nft_mint"}], "program": {"kind": "const", "value": [140, 151, 37, 143, 78, 36, 137, 241, 187, 61, 16, 41, 20, 142, 13, 131, 11, 90, 19, 153, 218, 255, 16, 132, 4, 142, 123, 216, 219, 233, 248, 89]}}}, {"name": "metadata_account", "docs": ["To store metaplex metadata"], "writable": true}, {"name": "pool_state", "docs": ["Add liquidity for this pool"], "writable": true}, {"name": "protocol_position", "docs": ["Store the information of market marking in range"], "writable": true, "pda": {"seeds": [{"kind": "const", "value": [112, 111, 115, 105, 116, 105, 111, 110]}, {"kind": "account", "path": "pool_state"}, {"kind": "arg", "path": "tick_lower_index"}, {"kind": "arg", "path": "tick_upper_index"}]}}, {"name": "tick_array_lower", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [116, 105, 99, 107, 95, 97, 114, 114, 97, 121]}, {"kind": "account", "path": "pool_state"}, {"kind": "arg", "path": "tick_array_lower_start_index"}]}}, {"name": "tick_array_upper", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [116, 105, 99, 107, 95, 97, 114, 114, 97, 121]}, {"kind": "account", "path": "pool_state"}, {"kind": "arg", "path": "tick_array_upper_start_index"}]}}, {"name": "personal_position", "docs": ["personal position state"], "writable": true, "pda": {"seeds": [{"kind": "const", "value": [112, 111, 115, 105, 116, 105, 111, 110]}, {"kind": "account", "path": "position_nft_mint"}]}}, {"name": "token_account_0", "docs": ["The token_0 account deposit token to the pool"], "writable": true}, {"name": "token_account_1", "docs": ["The token_1 account deposit token to the pool"], "writable": true}, {"name": "token_vault_0", "docs": ["The address that holds pool tokens for token_0"], "writable": true}, {"name": "token_vault_1", "docs": ["The address that holds pool tokens for token_1"], "writable": true}, {"name": "rent", "docs": ["Sysvar for token mint and ATA creation"], "address": "SysvarRent111111111111111111111111111111111"}, {"name": "system_program", "docs": ["Program to create the position manager state account"], "address": "11111111111111111111111111111111"}, {"name": "token_program", "docs": ["Program to create mint account and mint tokens"], "address": "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA"}, {"name": "associated_token_program", "docs": ["Program to create an ATA for receiving position NFT"], "address": "ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL"}, {"name": "metadata_program", "docs": ["Program to create NFT metadata"], "address": "metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s"}, {"name": "token_program_2022", "docs": ["Program to create mint account and mint tokens"], "address": "TokenzQdBNbLqP5VEhdkAS6EPFLC1PHnBqCXEpPxuEb"}, {"name": "vault_0_mint", "docs": ["The mint of token vault 0"]}, {"name": "vault_1_mint", "docs": ["The mint of token vault 1"]}], "args": [{"name": "tick_lower_index", "type": "i32"}, {"name": "tick_upper_index", "type": "i32"}, {"name": "tick_array_lower_start_index", "type": "i32"}, {"name": "tick_array_upper_start_index", "type": "i32"}, {"name": "liquidity", "type": "u128"}, {"name": "amount_0_max", "type": "u64"}, {"name": "amount_1_max", "type": "u64"}, {"name": "with_metadata", "type": "bool"}, {"name": "base_flag", "type": {"option": "bool"}}]}, {"name": "open_position_with_token22_nft", "docs": ["Creates a new position wrapped in a Token2022 NFT without relying on metadata_program and metadata_account, reduce the cost for user to create a personal position.", "", "# Arguments", "", "* `ctx` - The context of accounts", "* `tick_lower_index` - The low boundary of market", "* `tick_upper_index` - The upper boundary of market", "* `tick_array_lower_start_index` - The start index of tick array which include tick low", "* `tick_array_upper_start_index` - The start index of tick array which include tick upper", "* `liquidity` - The liquidity to be added, if zero, and the base_flag is specified, calculate liquidity base amount_0_max or amount_1_max according base_flag, otherwise open position with zero liquidity", "* `amount_0_max` - The max amount of token_0 to spend, which serves as a slippage check", "* `amount_1_max` - The max amount of token_1 to spend, which serves as a slippage check", "* `with_metadata` - The flag indicating whether to create NFT mint metadata", "* `base_flag` - if the liquidity specified as zero, true: calculate liquidity base amount_0_max otherwise base amount_1_max", ""], "discriminator": [77, 255, 174, 82, 125, 29, 201, 46], "accounts": [{"name": "payer", "docs": ["Pays to mint the position"], "writable": true, "signer": true}, {"name": "position_nft_owner"}, {"name": "position_nft_mint", "docs": ["Unique token mint address, initialize in contract"], "writable": true, "signer": true}, {"name": "position_nft_account", "writable": true}, {"name": "pool_state", "docs": ["Add liquidity for this pool"], "writable": true}, {"name": "protocol_position", "docs": ["Store the information of market marking in range"], "writable": true, "pda": {"seeds": [{"kind": "const", "value": [112, 111, 115, 105, 116, 105, 111, 110]}, {"kind": "account", "path": "pool_state"}, {"kind": "arg", "path": "tick_lower_index"}, {"kind": "arg", "path": "tick_upper_index"}]}}, {"name": "tick_array_lower", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [116, 105, 99, 107, 95, 97, 114, 114, 97, 121]}, {"kind": "account", "path": "pool_state"}, {"kind": "arg", "path": "tick_array_lower_start_index"}]}}, {"name": "tick_array_upper", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [116, 105, 99, 107, 95, 97, 114, 114, 97, 121]}, {"kind": "account", "path": "pool_state"}, {"kind": "arg", "path": "tick_array_upper_start_index"}]}}, {"name": "personal_position", "docs": ["personal position state"], "writable": true, "pda": {"seeds": [{"kind": "const", "value": [112, 111, 115, 105, 116, 105, 111, 110]}, {"kind": "account", "path": "position_nft_mint"}]}}, {"name": "token_account_0", "docs": ["The token_0 account deposit token to the pool"], "writable": true}, {"name": "token_account_1", "docs": ["The token_1 account deposit token to the pool"], "writable": true}, {"name": "token_vault_0", "docs": ["The address that holds pool tokens for token_0"], "writable": true}, {"name": "token_vault_1", "docs": ["The address that holds pool tokens for token_1"], "writable": true}, {"name": "rent", "docs": ["Sysvar for token mint and ATA creation"], "address": "SysvarRent111111111111111111111111111111111"}, {"name": "system_program", "docs": ["Program to create the position manager state account"], "address": "11111111111111111111111111111111"}, {"name": "token_program", "docs": ["Program to transfer for token account"], "address": "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA"}, {"name": "associated_token_program", "docs": ["Program to create an ATA for receiving position NFT"], "address": "ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL"}, {"name": "token_program_2022", "docs": ["Program to create NFT mint/token account and transfer for token22 account"], "address": "TokenzQdBNbLqP5VEhdkAS6EPFLC1PHnBqCXEpPxuEb"}, {"name": "vault_0_mint", "docs": ["The mint of token vault 0"]}, {"name": "vault_1_mint", "docs": ["The mint of token vault 1"]}], "args": [{"name": "tick_lower_index", "type": "i32"}, {"name": "tick_upper_index", "type": "i32"}, {"name": "tick_array_lower_start_index", "type": "i32"}, {"name": "tick_array_upper_start_index", "type": "i32"}, {"name": "liquidity", "type": "u128"}, {"name": "amount_0_max", "type": "u64"}, {"name": "amount_1_max", "type": "u64"}, {"name": "with_metadata", "type": "bool"}, {"name": "base_flag", "type": {"option": "bool"}}]}, {"name": "set_reward_params", "docs": ["Reset reward param, start a new reward cycle or extend the current cycle.", "", "# Arguments", "", "* `ctx` - The context of accounts", "* `reward_index` - The index of reward token in the pool, it must be smaller than 3", "* `emissions_per_second_x64` - The per second emission reward, when extend the current cycle,", "new value can't be less than old value", "* `open_time` - reward open timestamp, must be set when starting a new cycle", "* `end_time` - reward end timestamp", ""], "discriminator": [112, 52, 167, 75, 32, 201, 211, 137], "accounts": [{"name": "authority", "docs": ["Address to be set as protocol owner. It pays to create factory state account."], "signer": true}, {"name": "amm_config"}, {"name": "pool_state", "writable": true}, {"name": "operation_state", "docs": ["load info from the account to judge reward permission"], "pda": {"seeds": [{"kind": "const", "value": [111, 112, 101, 114, 97, 116, 105, 111, 110]}]}}, {"name": "token_program", "docs": ["Token program"], "address": "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA"}, {"name": "token_program_2022", "docs": ["Token program 2022"], "address": "TokenzQdBNbLqP5VEhdkAS6EPFLC1PHnBqCXEpPxuEb"}], "args": [{"name": "reward_index", "type": "u8"}, {"name": "emissions_per_second_x64", "type": "u128"}, {"name": "open_time", "type": "u64"}, {"name": "end_time", "type": "u64"}]}, {"name": "swap", "docs": ["#[deprecated(note = \"Use `swap_v2` instead.\")]", "Swaps one token for as much as possible of another token across a single pool", "", "# Arguments", "", "* `ctx` - The context of accounts", "* `amount` - Arranged in pairs with other_amount_threshold. (amount_in, amount_out_minimum) or (amount_out, amount_in_maximum)", "* `other_amount_threshold` - For slippage check", "* `sqrt_price_limit_x64` - The Q64.64 format √P limit price, and if it is 0, the maximum and minimum prices that can be reached are set by default according to the swap direction.", "* `is_base_input` - swap base input or swap base output", ""], "discriminator": [248, 198, 158, 145, 225, 117, 135, 200], "accounts": [{"name": "payer", "docs": ["The user performing the swap"], "signer": true}, {"name": "amm_config", "docs": ["The factory state to read protocol fees"]}, {"name": "pool_state", "docs": ["The program account of the pool in which the swap will be performed"], "writable": true}, {"name": "input_token_account", "docs": ["The user token account for input token"], "writable": true}, {"name": "output_token_account", "docs": ["The user token account for output token"], "writable": true}, {"name": "input_vault", "docs": ["The vault token account for input token"], "writable": true}, {"name": "output_vault", "docs": ["The vault token account for output token"], "writable": true}, {"name": "observation_state", "docs": ["The program account for the most recent oracle observation"], "writable": true}, {"name": "token_program", "docs": ["SPL program for token transfers"], "address": "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA"}, {"name": "tick_array", "writable": true}], "args": [{"name": "amount", "type": "u64"}, {"name": "other_amount_threshold", "type": "u64"}, {"name": "sqrt_price_limit_x64", "type": "u128"}, {"name": "is_base_input", "type": "bool"}]}, {"name": "swap_router_base_in", "docs": ["Swap token for as much as possible of another token across the path provided, base input", "", "# Arguments", "", "* `ctx` - The context of accounts", "* `amount_in` - Token amount to be swapped in", "* `amount_out_minimum` - Minimum output amount for slip control"], "discriminator": [69, 125, 115, 218, 245, 186, 242, 196], "accounts": [{"name": "payer", "docs": ["The user performing the swap"], "signer": true}, {"name": "input_token_account", "docs": ["The token account that pays input tokens for the swap"], "writable": true}, {"name": "input_token_mint", "docs": ["The mint of input token"], "writable": true}, {"name": "token_program", "docs": ["SPL program for token transfers"], "address": "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA"}, {"name": "token_program_2022", "docs": ["SPL program 2022 for token transfers"], "address": "TokenzQdBNbLqP5VEhdkAS6EPFLC1PHnBqCXEpPxuEb"}, {"name": "memo_program", "docs": ["Memo program"], "address": "MemoSq4gqABAXKb96qnH8TysNcWxMyWCqXgDLGmfcHr"}], "args": [{"name": "amount_in", "type": "u64"}, {"name": "amount_out_minimum", "type": "u64"}]}, {"name": "swap_v2", "docs": ["Swaps one token for as much as possible of another token across a single pool, support token program 2022", "", "# Arguments", "", "* `ctx` - The context of accounts", "* `amount` - Arranged in pairs with other_amount_threshold. (amount_in, amount_out_minimum) or (amount_out, amount_in_maximum)", "* `other_amount_threshold` - For slippage check", "* `sqrt_price_limit` - The Q64.64 format √P limit price, and if it is 0, the maximum and minimum prices that can be reached are set by default according to the swap direction.", "* `is_base_input` - swap base input or swap base output", ""], "discriminator": [43, 4, 237, 11, 26, 201, 30, 98], "accounts": [{"name": "payer", "docs": ["The user performing the swap"], "signer": true}, {"name": "amm_config", "docs": ["The factory state to read protocol fees"]}, {"name": "pool_state", "docs": ["The program account of the pool in which the swap will be performed"], "writable": true}, {"name": "input_token_account", "docs": ["The user token account for input token"], "writable": true}, {"name": "output_token_account", "docs": ["The user token account for output token"], "writable": true}, {"name": "input_vault", "docs": ["The vault token account for input token"], "writable": true}, {"name": "output_vault", "docs": ["The vault token account for output token"], "writable": true}, {"name": "observation_state", "docs": ["The program account for the most recent oracle observation"], "writable": true}, {"name": "token_program", "docs": ["SPL program for token transfers"], "address": "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA"}, {"name": "token_program_2022", "docs": ["SPL program 2022 for token transfers"], "address": "TokenzQdBNbLqP5VEhdkAS6EPFLC1PHnBqCXEpPxuEb"}, {"name": "memo_program", "docs": ["Memo program"], "address": "MemoSq4gqABAXKb96qnH8TysNcWxMyWCqXgDLGmfcHr"}, {"name": "input_vault_mint", "docs": ["The mint of token vault 0"]}, {"name": "output_vault_mint", "docs": ["The mint of token vault 1"]}], "args": [{"name": "amount", "type": "u64"}, {"name": "other_amount_threshold", "type": "u64"}, {"name": "sqrt_price_limit_x64", "type": "u128"}, {"name": "is_base_input", "type": "bool"}]}, {"name": "transfer_reward_owner", "docs": ["Transfer reward owner", "", "# Arguments", "", "* `ctx`- The context of accounts", "* `new_owner`- new owner pubkey", ""], "discriminator": [7, 22, 12, 83, 242, 43, 48, 121], "accounts": [{"name": "authority", "docs": ["Address to be set as operation account owner."], "signer": true, "address": "GThUX1Atko4tqhN2NaiTazWSeFWMuiUvfFnyJyUghFMJ"}, {"name": "pool_state", "writable": true}], "args": [{"name": "new_owner", "type": "pubkey"}]}, {"name": "update_amm_config", "docs": ["Updates the owner of the amm config", "Must be called by the current owner or admin", "", "# Arguments", "", "* `ctx`- The context of accounts", "* `trade_fee_rate`- The new trade fee rate of amm config, be set when `param` is 0", "* `protocol_fee_rate`- The new protocol fee rate of amm config, be set when `param` is 1", "* `fund_fee_rate`- The new fund fee rate of amm config, be set when `param` is 2", "* `new_owner`- The config's new owner, be set when `param` is 3", "* `new_fund_owner`- The config's new fund owner, be set when `param` is 4", "* `param`- The value can be 0 | 1 | 2 | 3 | 4, otherwise will report a error", ""], "discriminator": [49, 60, 174, 136, 154, 28, 116, 200], "accounts": [{"name": "owner", "docs": ["The amm config owner or admin"], "signer": true, "address": "GThUX1Atko4tqhN2NaiTazWSeFWMuiUvfFnyJyUghFMJ"}, {"name": "amm_config", "docs": ["Amm config account to be changed"], "writable": true}], "args": [{"name": "param", "type": "u8"}, {"name": "value", "type": "u32"}]}, {"name": "update_operation_account", "docs": ["Update the operation account", "", "# Arguments", "", "* `ctx`- The context of accounts", "* `param`- The value can be 0 | 1 | 2 | 3, otherwise will report a error", "* `keys`- update operation owner when the `param` is 0", "remove operation owner when the `param` is 1", "update whitelist mint when the `param` is 2", "remove whitelist mint when the `param` is 3", ""], "discriminator": [127, 70, 119, 40, 188, 227, 61, 7], "accounts": [{"name": "owner", "docs": ["Address to be set as operation account owner."], "signer": true, "address": "GThUX1Atko4tqhN2NaiTazWSeFWMuiUvfFnyJyUghFMJ"}, {"name": "operation_state", "docs": ["Initialize operation state account to store operation owner address and white list mint."], "writable": true, "pda": {"seeds": [{"kind": "const", "value": [111, 112, 101, 114, 97, 116, 105, 111, 110]}]}}, {"name": "system_program", "address": "11111111111111111111111111111111"}], "args": [{"name": "param", "type": "u8"}, {"name": "keys", "type": {"vec": "pubkey"}}]}, {"name": "update_pool_status", "docs": ["Update pool status for given value", "", "# Arguments", "", "* `ctx`- The context of accounts", "* `status` - The value of status", ""], "discriminator": [130, 87, 108, 6, 46, 224, 117, 123], "accounts": [{"name": "authority", "signer": true, "address": "GThUX1Atko4tqhN2NaiTazWSeFWMuiUvfFnyJyUghFMJ"}, {"name": "pool_state", "writable": true}], "args": [{"name": "status", "type": "u8"}]}, {"name": "update_reward_infos", "docs": ["Update rewards info of the given pool, can be called for everyone", "", "# Arguments", "", "* `ctx`- The context of accounts", ""], "discriminator": [163, 172, 224, 52, 11, 154, 106, 223], "accounts": [{"name": "pool_state", "docs": ["The liquidity pool for which reward info to update"], "writable": true}], "args": []}], "accounts": [{"name": "AmmConfig", "discriminator": [218, 244, 33, 104, 203, 203, 43, 111]}, {"name": "ObservationState", "discriminator": [122, 174, 197, 53, 129, 9, 165, 132]}, {"name": "OperationState", "discriminator": [19, 236, 58, 237, 81, 222, 183, 252]}, {"name": "PersonalPositionState", "discriminator": [70, 111, 150, 126, 230, 15, 25, 117]}, {"name": "PoolState", "discriminator": [247, 237, 227, 245, 215, 195, 222, 70]}, {"name": "ProtocolPositionState", "discriminator": [100, 226, 145, 99, 146, 218, 160, 106]}, {"name": "SupportMintAssociated", "discriminator": [134, 40, 183, 79, 12, 112, 162, 53]}, {"name": "TickArrayBitmapExtension", "discriminator": [60, 150, 36, 219, 97, 128, 139, 153]}, {"name": "TickArrayState", "discriminator": [192, 155, 85, 205, 49, 249, 129, 42]}], "events": [{"name": "CollectPersonalFeeEvent", "discriminator": [166, 174, 105, 192, 81, 161, 83, 105]}, {"name": "CollectProtocolFeeEvent", "discriminator": [206, 87, 17, 79, 45, 41, 213, 61]}, {"name": "ConfigChangeEvent", "discriminator": [247, 189, 7, 119, 106, 112, 95, 151]}, {"name": "CreatePersonalPositionEvent", "discriminator": [100, 30, 87, 249, 196, 223, 154, 206]}, {"name": "DecreaseLiquidityEvent", "discriminator": [58, 222, 86, 58, 68, 50, 85, 56]}, {"name": "IncreaseLiquidityEvent", "discriminator": [49, 79, 105, 212, 32, 34, 30, 84]}, {"name": "LiquidityCalculateEvent", "discriminator": [237, 112, 148, 230, 57, 84, 180, 162]}, {"name": "LiquidityChangeEvent", "discriminator": [126, 240, 175, 206, 158, 88, 153, 107]}, {"name": "PoolCreatedEvent", "discriminator": [25, 94, 75, 47, 112, 99, 53, 63]}, {"name": "SwapEvent", "discriminator": [64, 198, 205, 232, 38, 8, 113, 226]}, {"name": "UpdateRewardInfosEvent", "discriminator": [109, 127, 186, 78, 114, 65, 37, 236]}], "errors": [{"code": 6000, "name": "LOK", "msg": "LOK"}, {"code": 6001, "name": "NotApproved", "msg": "Not approved"}, {"code": 6002, "name": "InvalidUpdateConfigFlag", "msg": "invalid update amm config flag"}, {"code": 6003, "name": "Account<PERSON>ack", "msg": "Account lack"}, {"code": 6004, "name": "ClosePositionErr", "msg": "Remove liquitity, collect fees owed and reward then you can close position account"}, {"code": 6005, "name": "ZeroMintAmount", "msg": "Minting amount should be greater than 0"}, {"code": 6006, "name": "InvalidTickIndex", "msg": "Tick out of range"}, {"code": 6007, "name": "TickInvalidOrder", "msg": "The lower tick must be below the upper tick"}, {"code": 6008, "name": "TickLowerOverflow", "msg": "The tick must be greater, or equal to the minimum tick(-443636)"}, {"code": 6009, "name": "TickUpperOverflow", "msg": "The tick must be lesser than, or equal to the maximum tick(443636)"}, {"code": 6010, "name": "TickAndSpacingNotMatch", "msg": "tick % tick_spacing must be zero"}, {"code": 6011, "name": "InvalidTickArray", "msg": "Invalid tick array account"}, {"code": 6012, "name": "InvalidTickArrayBoundary", "msg": "Invalid tick array boundary"}, {"code": 6013, "name": "SqrtPriceLimitOverflow", "msg": "Square root price limit overflow"}, {"code": 6014, "name": "SqrtPriceX64", "msg": "sqrt_price_x64 out of range"}, {"code": 6015, "name": "LiquiditySubValueErr", "msg": "Liquidity sub delta L must be smaller than before"}, {"code": 6016, "name": "LiquidityAddValueErr", "msg": "Liquidity add delta L must be greater, or equal to before"}, {"code": 6017, "name": "InvalidLiquidity", "msg": "Invalid liquidity when update position"}, {"code": 6018, "name": "ForbidBothZeroForSupplyLiquidity", "msg": "Both token amount must not be zero while supply liquidity"}, {"code": 6019, "name": "LiquidityInsufficient", "msg": "Liquidity insufficient"}, {"code": 6020, "name": "TransactionTooOld", "msg": "Transaction too old"}, {"code": 6021, "name": "PriceSlippageCheck", "msg": "Price slippage check"}, {"code": 6022, "name": "TooLittleOutputReceived", "msg": "Too little output received"}, {"code": 6023, "name": "TooMuchInputPaid", "msg": "Too much input paid"}, {"code": 6024, "name": "ZeroAmountSpecified", "msg": "Swap special amount can not be zero"}, {"code": 6025, "name": "InvalidInputPoolVault", "msg": "Input pool vault is invalid"}, {"code": 6026, "name": "TooSmallInputOrOutputAmount", "msg": "Swap input or output amount is too small"}, {"code": 6027, "name": "NotEnoughTickArrayAccount", "msg": "Not enought tick array account"}, {"code": 6028, "name": "InvalidFirstTickArrayAccount", "msg": "Invalid first tick array account"}, {"code": 6029, "name": "InvalidRewardIndex", "msg": "Invalid reward index"}, {"code": 6030, "name": "FullRewardInfo", "msg": "The init reward token reach to the max"}, {"code": 6031, "name": "RewardTokenAlreadyInUse", "msg": "The init reward token already in use"}, {"code": 6032, "name": "ExceptRewardMint", "msg": "The reward tokens must contain one of pool vault mint except the last reward"}, {"code": 6033, "name": "InvalidRewardInitParam", "msg": "Invalid reward init param"}, {"code": 6034, "name": "InvalidRewardDesiredAmount", "msg": "Invalid collect reward desired amount"}, {"code": 6035, "name": "InvalidRewardInputAccountNumber", "msg": "Invalid collect reward input account number"}, {"code": 6036, "name": "InvalidRewardPeriod", "msg": "Invalid reward period"}, {"code": 6037, "name": "NotApproveUpdateRewardEmissiones", "msg": "Modification of emissiones is allowed within 72 hours from the end of the previous cycle"}, {"code": 6038, "name": "UnInitializedRewardInfo", "msg": "uninitialized reward info"}, {"code": 6039, "name": "NotSupportMint", "msg": "Not support token_2022 mint extension"}, {"code": 6040, "name": "MissingTickArrayBitmapExtensionAccount", "msg": "Missing tickarray bitmap extension account"}, {"code": 6041, "name": "InsufficientLiquidityForDirection", "msg": "Insufficient liquidity for this direction"}, {"code": 6042, "name": "MaxTokenOverflow", "msg": "Max token overflow"}, {"code": 6043, "name": "CalculateOverflow", "msg": "Calculate overflow"}, {"code": 6044, "name": "TransferFeeCalculateNotMatch", "msg": "TransferFee calculate not match"}], "types": [{"name": "AmmConfig", "docs": ["Holds the current owner of the factory"], "type": {"kind": "struct", "fields": [{"name": "bump", "docs": ["Bump to identify PDA"], "type": "u8"}, {"name": "index", "type": "u16"}, {"name": "owner", "docs": ["Address of the protocol owner"], "type": "pubkey"}, {"name": "protocol_fee_rate", "docs": ["The protocol fee"], "type": "u32"}, {"name": "trade_fee_rate", "docs": ["The trade fee, denominated in hundredths of a bip (10^-6)"], "type": "u32"}, {"name": "tick_spacing", "docs": ["The tick spacing"], "type": "u16"}, {"name": "fund_fee_rate", "docs": ["The fund fee, denominated in hundredths of a bip (10^-6)"], "type": "u32"}, {"name": "padding_u32", "type": "u32"}, {"name": "fund_owner", "type": "pubkey"}, {"name": "padding", "type": {"array": ["u64", 3]}}]}}, {"name": "CollectPersonalFeeEvent", "docs": ["Emitted when tokens are collected for a position"], "type": {"kind": "struct", "fields": [{"name": "position_nft_mint", "docs": ["The ID of the token for which underlying tokens were collected"], "type": "pubkey"}, {"name": "recipient_token_account_0", "docs": ["The token account that received the collected token_0 tokens"], "type": "pubkey"}, {"name": "recipient_token_account_1", "docs": ["The token account that received the collected token_1 tokens"], "type": "pubkey"}, {"name": "amount_0", "docs": ["The amount of token_0 owed to the position that was collected"], "type": "u64"}, {"name": "amount_1", "docs": ["The amount of token_1 owed to the position that was collected"], "type": "u64"}]}}, {"name": "CollectProtocolFeeEvent", "docs": ["Emitted when the collected protocol fees are withdrawn by the factory owner"], "type": {"kind": "struct", "fields": [{"name": "pool_state", "docs": ["The pool whose protocol fee is collected"], "type": "pubkey"}, {"name": "recipient_token_account_0", "docs": ["The address that receives the collected token_0 protocol fees"], "type": "pubkey"}, {"name": "recipient_token_account_1", "docs": ["The address that receives the collected token_1 protocol fees"], "type": "pubkey"}, {"name": "amount_0", "docs": ["The amount of token_0 protocol fees that is withdrawn"], "type": "u64"}, {"name": "amount_1", "docs": ["The amount of token_0 protocol fees that is withdrawn"], "type": "u64"}]}}, {"name": "ConfigChangeEvent", "docs": ["Emitted when create or update a config"], "type": {"kind": "struct", "fields": [{"name": "index", "type": "u16"}, {"name": "owner", "type": "pubkey"}, {"name": "protocol_fee_rate", "type": "u32"}, {"name": "trade_fee_rate", "type": "u32"}, {"name": "tick_spacing", "type": "u16"}, {"name": "fund_fee_rate", "type": "u32"}, {"name": "fund_owner", "type": "pubkey"}]}}, {"name": "CreatePersonalPositionEvent", "docs": ["Emitted when create a new position"], "type": {"kind": "struct", "fields": [{"name": "pool_state", "docs": ["The pool for which liquidity was added"], "type": "pubkey"}, {"name": "minter", "docs": ["The address that create the position"], "type": "pubkey"}, {"name": "nft_owner", "docs": ["The owner of the position and recipient of any minted liquidity"], "type": "pubkey"}, {"name": "tick_lower_index", "docs": ["The lower tick of the position"], "type": "i32"}, {"name": "tick_upper_index", "docs": ["The upper tick of the position"], "type": "i32"}, {"name": "liquidity", "docs": ["The amount of liquidity minted to the position range"], "type": "u128"}, {"name": "deposit_amount_0", "docs": ["The amount of token_0 was deposit for the liquidity"], "type": "u64"}, {"name": "deposit_amount_1", "docs": ["The amount of token_1 was deposit for the liquidity"], "type": "u64"}, {"name": "deposit_amount_0_transfer_fee", "docs": ["The token transfer fee for deposit_amount_0"], "type": "u64"}, {"name": "deposit_amount_1_transfer_fee", "docs": ["The token transfer fee for deposit_amount_1"], "type": "u64"}]}}, {"name": "DecreaseLiquidityEvent", "docs": ["Emitted when liquidity is decreased."], "type": {"kind": "struct", "fields": [{"name": "position_nft_mint", "docs": ["The ID of the token for which liquidity was decreased"], "type": "pubkey"}, {"name": "liquidity", "docs": ["The amount by which liquidity for the position was decreased"], "type": "u128"}, {"name": "decrease_amount_0", "docs": ["The amount of token_0 that was paid for the decrease in liquidity"], "type": "u64"}, {"name": "decrease_amount_1", "docs": ["The amount of token_1 that was paid for the decrease in liquidity"], "type": "u64"}, {"name": "fee_amount_0", "type": "u64"}, {"name": "fee_amount_1", "docs": ["The amount of token_1 fee"], "type": "u64"}, {"name": "reward_amounts", "docs": ["The amount of rewards"], "type": {"array": ["u64", 3]}}, {"name": "transfer_fee_0", "docs": ["The amount of token_0 transfer fee"], "type": "u64"}, {"name": "transfer_fee_1", "docs": ["The amount of token_1 transfer fee"], "type": "u64"}]}}, {"name": "IncreaseLiquidityEvent", "docs": ["Emitted when liquidity is increased."], "type": {"kind": "struct", "fields": [{"name": "position_nft_mint", "docs": ["The ID of the token for which liquidity was increased"], "type": "pubkey"}, {"name": "liquidity", "docs": ["The amount by which liquidity for the NFT position was increased"], "type": "u128"}, {"name": "amount_0", "docs": ["The amount of token_0 that was paid for the increase in liquidity"], "type": "u64"}, {"name": "amount_1", "docs": ["The amount of token_1 that was paid for the increase in liquidity"], "type": "u64"}, {"name": "amount_0_transfer_fee", "docs": ["The token transfer fee for amount_0"], "type": "u64"}, {"name": "amount_1_transfer_fee", "docs": ["The token transfer fee for amount_1"], "type": "u64"}]}}, {"name": "InitializeRewardParam", "type": {"kind": "struct", "fields": [{"name": "open_time", "docs": ["Reward open time"], "type": "u64"}, {"name": "end_time", "docs": ["Reward end time"], "type": "u64"}, {"name": "emissions_per_second_x64", "docs": ["Token reward per second are earned per unit of liquidity"], "type": "u128"}]}}, {"name": "LiquidityCalculateEvent", "docs": ["Emitted when liquidity decreased or increase."], "type": {"kind": "struct", "fields": [{"name": "pool_liquidity", "docs": ["The pool liquidity before decrease or increase"], "type": "u128"}, {"name": "pool_sqrt_price_x64", "docs": ["The pool price when decrease or increase in liquidity"], "type": "u128"}, {"name": "pool_tick", "docs": ["The pool tick when decrease or increase in liquidity"], "type": "i32"}, {"name": "calc_amount_0", "docs": ["The amount of token_0 that was calculated for the decrease or increase in liquidity"], "type": "u64"}, {"name": "calc_amount_1", "docs": ["The amount of token_1 that was calculated for the decrease or increase in liquidity"], "type": "u64"}, {"name": "trade_fee_owed_0", "type": "u64"}, {"name": "trade_fee_owed_1", "docs": ["The amount of token_1 fee"], "type": "u64"}, {"name": "transfer_fee_0", "docs": ["The amount of token_0 transfer fee without trade_fee_amount_0"], "type": "u64"}, {"name": "transfer_fee_1", "docs": ["The amount of token_1 transfer fee without trade_fee_amount_0"], "type": "u64"}]}}, {"name": "LiquidityChangeEvent", "docs": ["Emitted pool liquidity change when increase and decrease liquidity"], "type": {"kind": "struct", "fields": [{"name": "pool_state", "docs": ["The pool for swap"], "type": "pubkey"}, {"name": "tick", "docs": ["The tick of the pool"], "type": "i32"}, {"name": "tick_lower", "docs": ["The tick lower of position"], "type": "i32"}, {"name": "tick_upper", "docs": ["The tick lower of position"], "type": "i32"}, {"name": "liquidity_before", "docs": ["The liquidity of the pool before liquidity change"], "type": "u128"}, {"name": "liquidity_after", "docs": ["The liquidity of the pool after liquidity change"], "type": "u128"}]}}, {"name": "Observation", "docs": ["The element of observations in ObservationState"], "serialization": "bytemuckunsafe", "repr": {"kind": "c", "packed": true}, "type": {"kind": "struct", "fields": [{"name": "block_timestamp", "docs": ["The block timestamp of the observation"], "type": "u32"}, {"name": "tick_cumulative", "docs": ["the cumulative of tick during the duration time"], "type": "i64"}, {"name": "padding", "docs": ["padding for feature update"], "type": {"array": ["u64", 4]}}]}}, {"name": "ObservationState", "serialization": "bytemuckunsafe", "repr": {"kind": "c", "packed": true}, "type": {"kind": "struct", "fields": [{"name": "initialized", "docs": ["Whether the ObservationState is initialized"], "type": "bool"}, {"name": "recent_epoch", "docs": ["recent update epoch"], "type": "u64"}, {"name": "observation_index", "docs": ["the most-recently updated index of the observations array"], "type": "u16"}, {"name": "pool_id", "docs": ["belongs to which pool"], "type": "pubkey"}, {"name": "observations", "docs": ["observation array"], "type": {"array": [{"defined": {"name": "Observation"}}, 100]}}, {"name": "padding", "docs": ["padding for feature update"], "type": {"array": ["u64", 4]}}]}}, {"name": "OperationState", "docs": ["Holds the current owner of the factory"], "serialization": "bytemuckunsafe", "repr": {"kind": "c", "packed": true}, "type": {"kind": "struct", "fields": [{"name": "bump", "docs": ["Bump to identify PDA"], "type": "u8"}, {"name": "operation_owners", "docs": ["Address of the operation owner"], "type": {"array": ["pubkey", 10]}}, {"name": "whitelist_mints", "docs": ["The mint address of whitelist to emit reward"], "type": {"array": ["pubkey", 100]}}]}}, {"name": "PersonalPositionState", "type": {"kind": "struct", "fields": [{"name": "bump", "docs": ["Bump to identify PDA"], "type": {"array": ["u8", 1]}}, {"name": "nft_mint", "docs": ["Mint address of the tokenized position"], "type": "pubkey"}, {"name": "pool_id", "docs": ["The ID of the pool with which this token is connected"], "type": "pubkey"}, {"name": "tick_lower_index", "docs": ["The lower bound tick of the position"], "type": "i32"}, {"name": "tick_upper_index", "docs": ["The upper bound tick of the position"], "type": "i32"}, {"name": "liquidity", "docs": ["The amount of liquidity owned by this position"], "type": "u128"}, {"name": "fee_growth_inside_0_last_x64", "docs": ["The token_0 fee growth of the aggregate position as of the last action on the individual position"], "type": "u128"}, {"name": "fee_growth_inside_1_last_x64", "docs": ["The token_1 fee growth of the aggregate position as of the last action on the individual position"], "type": "u128"}, {"name": "token_fees_owed_0", "docs": ["The fees owed to the position owner in token_0, as of the last computation"], "type": "u64"}, {"name": "token_fees_owed_1", "docs": ["The fees owed to the position owner in token_1, as of the last computation"], "type": "u64"}, {"name": "reward_infos", "type": {"array": [{"defined": {"name": "PositionRewardInfo"}}, 3]}}, {"name": "recent_epoch", "type": "u64"}, {"name": "padding", "type": {"array": ["u64", 7]}}]}}, {"name": "PoolCreatedEvent", "docs": ["Emitted when a pool is created and initialized with a starting price", ""], "type": {"kind": "struct", "fields": [{"name": "token_mint_0", "docs": ["The first token of the pool by address sort order"], "type": "pubkey"}, {"name": "token_mint_1", "docs": ["The second token of the pool by address sort order"], "type": "pubkey"}, {"name": "tick_spacing", "docs": ["The minimum number of ticks between initialized ticks"], "type": "u16"}, {"name": "pool_state", "docs": ["The address of the created pool"], "type": "pubkey"}, {"name": "sqrt_price_x64", "docs": ["The initial sqrt price of the pool, as a Q64.64"], "type": "u128"}, {"name": "tick", "docs": ["The initial tick of the pool, i.e. log base 1.0001 of the starting price of the pool"], "type": "i32"}, {"name": "token_vault_0", "docs": ["Vault of token_0"], "type": "pubkey"}, {"name": "token_vault_1", "docs": ["Vault of token_1"], "type": "pubkey"}]}}, {"name": "PoolState", "docs": ["The pool state", "", "PDA of `[POOL_SEED, config, token_mint_0, token_mint_1]`", ""], "serialization": "bytemuckunsafe", "repr": {"kind": "c", "packed": true}, "type": {"kind": "struct", "fields": [{"name": "bump", "docs": ["Bump to identify PDA"], "type": {"array": ["u8", 1]}}, {"name": "amm_config", "type": "pubkey"}, {"name": "owner", "type": "pubkey"}, {"name": "token_mint_0", "docs": ["Token pair of the pool, where token_mint_0 address < token_mint_1 address"], "type": "pubkey"}, {"name": "token_mint_1", "type": "pubkey"}, {"name": "token_vault_0", "docs": ["Token pair vault"], "type": "pubkey"}, {"name": "token_vault_1", "type": "pubkey"}, {"name": "observation_key", "docs": ["observation account key"], "type": "pubkey"}, {"name": "mint_decimals_0", "docs": ["mint0 and mint1 decimals"], "type": "u8"}, {"name": "mint_decimals_1", "type": "u8"}, {"name": "tick_spacing", "docs": ["The minimum number of ticks between initialized ticks"], "type": "u16"}, {"name": "liquidity", "docs": ["The currently in range liquidity available to the pool."], "type": "u128"}, {"name": "sqrt_price_x64", "docs": ["The current price of the pool as a sqrt(token_1/token_0) Q64.64 value"], "type": "u128"}, {"name": "tick_current", "docs": ["The current tick of the pool, i.e. according to the last tick transition that was run."], "type": "i32"}, {"name": "padding3", "type": "u16"}, {"name": "padding4", "type": "u16"}, {"name": "fee_growth_global_0_x64", "docs": ["The fee growth as a Q64.64 number, i.e. fees of token_0 and token_1 collected per", "unit of liquidity for the entire life of the pool."], "type": "u128"}, {"name": "fee_growth_global_1_x64", "type": "u128"}, {"name": "protocol_fees_token_0", "docs": ["The amounts of token_0 and token_1 that are owed to the protocol."], "type": "u64"}, {"name": "protocol_fees_token_1", "type": "u64"}, {"name": "swap_in_amount_token_0", "docs": ["The amounts in and out of swap token_0 and token_1"], "type": "u128"}, {"name": "swap_out_amount_token_1", "type": "u128"}, {"name": "swap_in_amount_token_1", "type": "u128"}, {"name": "swap_out_amount_token_0", "type": "u128"}, {"name": "status", "docs": ["Bitwise representation of the state of the pool", "bit0, 1: disable open position and increase liquidity, 0: normal", "bit1, 1: disable decrease liquidity, 0: normal", "bit2, 1: disable collect fee, 0: normal", "bit3, 1: disable collect reward, 0: normal", "bit4, 1: disable swap, 0: normal"], "type": "u8"}, {"name": "padding", "docs": ["Leave blank for future use"], "type": {"array": ["u8", 7]}}, {"name": "reward_infos", "type": {"array": [{"defined": {"name": "RewardInfo"}}, 3]}}, {"name": "tick_array_bitmap", "docs": ["Packed initialized tick array state"], "type": {"array": ["u64", 16]}}, {"name": "total_fees_token_0", "docs": ["except protocol_fee and fund_fee"], "type": "u64"}, {"name": "total_fees_claimed_token_0", "docs": ["except protocol_fee and fund_fee"], "type": "u64"}, {"name": "total_fees_token_1", "type": "u64"}, {"name": "total_fees_claimed_token_1", "type": "u64"}, {"name": "fund_fees_token_0", "type": "u64"}, {"name": "fund_fees_token_1", "type": "u64"}, {"name": "open_time", "type": "u64"}, {"name": "recent_epoch", "type": "u64"}, {"name": "padding1", "type": {"array": ["u64", 24]}}, {"name": "padding2", "type": {"array": ["u64", 32]}}]}}, {"name": "PositionRewardInfo", "type": {"kind": "struct", "fields": [{"name": "growth_inside_last_x64", "type": "u128"}, {"name": "reward_amount_owed", "type": "u64"}]}}, {"name": "ProtocolPositionState", "docs": ["Info stored for each user's position"], "type": {"kind": "struct", "fields": [{"name": "bump", "docs": ["Bump to identify PDA"], "type": "u8"}, {"name": "pool_id", "docs": ["The ID of the pool with which this token is connected"], "type": "pubkey"}, {"name": "tick_lower_index", "docs": ["The lower bound tick of the position"], "type": "i32"}, {"name": "tick_upper_index", "docs": ["The upper bound tick of the position"], "type": "i32"}, {"name": "liquidity", "docs": ["The amount of liquidity owned by this position"], "type": "u128"}, {"name": "fee_growth_inside_0_last_x64", "docs": ["The token_0 fee growth per unit of liquidity as of the last update to liquidity or fees owed"], "type": "u128"}, {"name": "fee_growth_inside_1_last_x64", "docs": ["The token_1 fee growth per unit of liquidity as of the last update to liquidity or fees owed"], "type": "u128"}, {"name": "token_fees_owed_0", "docs": ["The fees owed to the position owner in token_0"], "type": "u64"}, {"name": "token_fees_owed_1", "docs": ["The fees owed to the position owner in token_1"], "type": "u64"}, {"name": "reward_growth_inside", "docs": ["The reward growth per unit of liquidity as of the last update to liquidity"], "type": {"array": ["u128", 3]}}, {"name": "recent_epoch", "type": "u64"}, {"name": "padding", "type": {"array": ["u64", 7]}}]}}, {"name": "RewardInfo", "serialization": "bytemuckunsafe", "repr": {"kind": "c", "packed": true}, "type": {"kind": "struct", "fields": [{"name": "reward_state", "docs": ["Reward state"], "type": "u8"}, {"name": "open_time", "docs": ["Reward open time"], "type": "u64"}, {"name": "end_time", "docs": ["Reward end time"], "type": "u64"}, {"name": "last_update_time", "docs": ["Reward last update time"], "type": "u64"}, {"name": "emissions_per_second_x64", "docs": ["Q64.64 number indicates how many tokens per second are earned per unit of liquidity."], "type": "u128"}, {"name": "reward_total_emissioned", "docs": ["The total amount of reward emissioned"], "type": "u64"}, {"name": "reward_claimed", "docs": ["The total amount of claimed reward"], "type": "u64"}, {"name": "token_mint", "docs": ["Reward token mint."], "type": "pubkey"}, {"name": "token_vault", "docs": ["Reward vault token account."], "type": "pubkey"}, {"name": "authority", "docs": ["The owner that has permission to set reward param"], "type": "pubkey"}, {"name": "reward_growth_global_x64", "docs": ["Q64.64 number that tracks the total tokens earned per unit of liquidity since the reward", "emissions were turned on."], "type": "u128"}]}}, {"name": "SupportMintAssociated", "docs": ["Holds the current owner of the factory"], "type": {"kind": "struct", "fields": [{"name": "bump", "docs": ["Bump to identify PDA"], "type": "u8"}, {"name": "mint", "docs": ["Address of the supported token22 mint"], "type": "pubkey"}, {"name": "padding", "type": {"array": ["u64", 8]}}]}}, {"name": "SwapEvent", "docs": ["Emitted by when a swap is performed for a pool"], "type": {"kind": "struct", "fields": [{"name": "pool_state", "docs": ["The pool for which token_0 and token_1 were swapped"], "type": "pubkey"}, {"name": "sender", "docs": ["The address that initiated the swap call, and that received the callback"], "type": "pubkey"}, {"name": "token_account_0", "docs": ["The payer token account in zero for one swaps, or the recipient token account", "in one for zero swaps"], "type": "pubkey"}, {"name": "token_account_1", "docs": ["The payer token account in one for zero swaps, or the recipient token account", "in zero for one swaps"], "type": "pubkey"}, {"name": "amount_0", "docs": ["The real delta amount of the token_0 of the pool or user"], "type": "u64"}, {"name": "transfer_fee_0", "docs": ["The transfer fee charged by the withheld_amount of the token_0"], "type": "u64"}, {"name": "amount_1", "docs": ["The real delta of the token_1 of the pool or user"], "type": "u64"}, {"name": "transfer_fee_1", "docs": ["The transfer fee charged by the withheld_amount of the token_1"], "type": "u64"}, {"name": "zero_for_one", "docs": ["if true, amount_0 is negtive and amount_1 is positive"], "type": "bool"}, {"name": "sqrt_price_x64", "docs": ["The sqrt(price) of the pool after the swap, as a Q64.64"], "type": "u128"}, {"name": "liquidity", "docs": ["The liquidity of the pool after the swap"], "type": "u128"}, {"name": "tick", "docs": ["The log base 1.0001 of price of the pool after the swap"], "type": "i32"}]}}, {"name": "TickArrayBitmapExtension", "serialization": "bytemuckunsafe", "repr": {"kind": "c", "packed": true}, "type": {"kind": "struct", "fields": [{"name": "pool_id", "type": "pubkey"}, {"name": "positive_tick_array_bitmap", "docs": ["Packed initialized tick array state for start_tick_index is positive"], "type": {"array": [{"array": ["u64", 8]}, 14]}}, {"name": "negative_tick_array_bitmap", "docs": ["Packed initialized tick array state for start_tick_index is negitive"], "type": {"array": [{"array": ["u64", 8]}, 14]}}]}}, {"name": "TickArrayState", "serialization": "bytemuckunsafe", "repr": {"kind": "c", "packed": true}, "type": {"kind": "struct", "fields": [{"name": "pool_id", "type": "pubkey"}, {"name": "start_tick_index", "type": "i32"}, {"name": "ticks", "type": {"array": [{"defined": {"name": "TickState"}}, 60]}}, {"name": "initialized_tick_count", "type": "u8"}, {"name": "recent_epoch", "type": "u64"}, {"name": "padding", "type": {"array": ["u8", 107]}}]}}, {"name": "TickState", "serialization": "bytemuckunsafe", "repr": {"kind": "c", "packed": true}, "type": {"kind": "struct", "fields": [{"name": "tick", "type": "i32"}, {"name": "liquidity_net", "docs": ["Amount of net liquidity added (subtracted) when tick is crossed from left to right (right to left)"], "type": "i128"}, {"name": "liquidity_gross", "docs": ["The total position liquidity that references this tick"], "type": "u128"}, {"name": "fee_growth_outside_0_x64", "docs": ["Fee growth per unit of liquidity on the _other_ side of this tick (relative to the current tick)", "only has relative meaning, not absolute — the value depends on when the tick is initialized"], "type": "u128"}, {"name": "fee_growth_outside_1_x64", "type": "u128"}, {"name": "reward_growths_outside_x64", "type": {"array": ["u128", 3]}}, {"name": "padding", "type": {"array": ["u32", 13]}}]}}, {"name": "UpdateRewardInfosEvent", "docs": ["Emitted when Reward are updated for a pool"], "type": {"kind": "struct", "fields": [{"name": "reward_growth_global_x64", "docs": ["Reward info"], "type": {"array": ["u128", 3]}}]}}]}