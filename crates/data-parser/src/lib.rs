//! # Data Parser
//!
//! 可插拔的DEX数据解析器模块 - 基于sol-feeder的EventParser架构重构
//! 提供统一的接口来解析不同DEX的交易数据

pub mod parser;
pub mod events;
pub mod registry;
pub mod parsers;
pub mod transfer_parser;
pub mod accounts;
pub mod anchor_types;
mod utils;

use std::collections::HashMap;
use solana_sdk::bs58;
use solana_sdk::instruction::CompiledInstruction;
use solana_sdk::pubkey::Pubkey;
use solana_transaction_status::UiCompiledInstruction;
use yellowstone_grpc_proto::prost_types::Timestamp;
// 重新导出主要的类型和trait
pub use parser::{
    DexParser, ParseResult, ParserError, parser_utils
};
pub use events::{
    DexEvent, DexEventType, ProtocolType, EventMetadata, TransferData,
    // Raydium CLMM events
    RaydiumClmmSwapEvent, RaydiumClmmSwapV2Event,
    RaydiumClmmIncreaseLiquidityV2Event, RaydiumClmmDecreaseLiquidityV2Event,
    LiquidityChangeAccounts,
    // Raydium CPMM events
    RaydiumCpmmSwapBaseInputEvent, RaydiumCpmmSwapBaseOutputEvent,
    // PumpFun events
    PumpFunCreateTokenEvent, PumpFunTradeEvent,
    // Bonk events
    BonkTradeEvent, BonkPoolCreateEvent,
};
pub use registry::{
    ParserRegistry, ParserMetadata, ParserStats,
    global_registry, register_global_parser, find_global_parsers_for_program
};
pub use transfer_parser::{
    parse_transfer_datas_from_instructions, set_transfer_data_for_events
};
pub use parsers::*;
// 导出账号解析相关类型和功能
pub use accounts::{
    // 核心trait和类型
    AccountParser, AccountData, AccountType, ParsedAccount,
    // 注册表
    AccountParserRegistry, AccountParserMetadata, global_account_registry,
    // Anchor 解析器管理器
    AnchorParserManager, create_anchor_parser_manager,
};

use crate::parser_utils::{matches_discriminator, matches_discriminator_str, validate_account_indices};

/// 内联指令事件解析器
pub type InnerInstructionEventParser =
fn(data: &[u8], metadata: EventMetadata) -> Option<Box<dyn DexEvent>>;

/// 指令事件解析器
pub type InstructionEventParser =
fn(data: &[u8], accounts: &[Pubkey], metadata: EventMetadata) -> Option<Box<dyn DexEvent>>;


/// 通用事件解析器配置
#[derive(Debug, Clone)]
pub struct GenericEventParseConfig {
    pub inner_instruction_discriminator: &'static str,
    pub instruction_discriminator: &'static [u8],
    pub event_type: DexEventType,
    pub inner_instruction_parser: InnerInstructionEventParser,
    pub instruction_parser: InstructionEventParser,
}


/// 通用事件解析器基类
pub struct GenericEventParser {
    name: String,
    program_id: Pubkey,
    protocol_type: ProtocolType,
    inner_instruction_configs: HashMap<&'static str, Vec<GenericEventParseConfig>>,
    instruction_configs: HashMap<Vec<u8>, Vec<GenericEventParseConfig>>,
}


impl GenericEventParser {
    /// 创建新的通用事件解析器
    pub fn new(
        name: String,
        program_id: Pubkey,
        protocol_type: ProtocolType,
        configs: Vec<GenericEventParseConfig>,
    ) -> Self {
        let mut inner_instruction_configs = HashMap::new();
        let mut instruction_configs = HashMap::new();

        for config in configs {
            inner_instruction_configs
                .entry(config.inner_instruction_discriminator)
                .or_insert(vec![])
                .push(config.clone());
            instruction_configs
                .entry(config.instruction_discriminator.to_vec())
                .or_insert(vec![])
                .push(config);
        }

        Self {
            name,
            program_id,
            protocol_type,
            inner_instruction_configs,
            instruction_configs,
        }
    }

    /// 通用的内联指令解析方法
    fn parse_inner_instruction_event(
        &self,
        config: &GenericEventParseConfig,
        data: &[u8],
        signature: &str,
        slot: u64,
        block_time: Option<Timestamp>,
        index: String,
    ) -> Option<Box<dyn DexEvent>> {
        let timestamp = block_time.unwrap_or(Timestamp {
            seconds: 0,
            nanos: 0,
        });
        let block_time_ms = timestamp.seconds * 1000 + (timestamp.nanos as i64) / 1_000_000;
        let metadata = EventMetadata::new(
            signature.to_string(),
            signature.to_string(),
            slot,
            timestamp.seconds,
            block_time_ms,
            self.protocol_type.clone(),
            config.event_type.clone(),
            self.program_id,
            index,
        );
        (config.inner_instruction_parser)(data, metadata)
    }


    /// 通用的指令解析方法
    fn parse_instruction_event(
        &self,
        config: &GenericEventParseConfig,
        data: &[u8],
        account_pubkeys: &[Pubkey],
        signature: &str,
        slot: u64,
        block_time: Option<Timestamp>,
        index: String,
    ) -> Option<Box<dyn DexEvent>> {
        let timestamp = block_time.unwrap_or(Timestamp {
            seconds: 0,
            nanos: 0,
        });
        let block_time_ms = timestamp.seconds * 1000 + (timestamp.nanos as i64) / 1_000_000;
        let metadata = EventMetadata::new(
            signature.to_string(),
            signature.to_string(),
            slot,
            timestamp.seconds,
            block_time_ms,
            self.protocol_type.clone(),
            config.event_type.clone(),
            self.program_id,
            index,
        );
        (config.instruction_parser)(data, account_pubkeys, metadata)
    }

}

#[async_trait::async_trait]
impl DexParser for GenericEventParser {
    fn name(&self) -> &str {
        self.name.as_str()
    }

    fn supported_program_ids(&self) -> Vec<Pubkey> {
        vec![self.program_id]
    }

    fn should_handle(&self, program_id: &Pubkey) -> bool {
        *program_id == self.program_id
    }


    /// 从指令中解析事件数据
    fn parse_events_from_instruction(
        &self,
        instruction: &CompiledInstruction,
        accounts: &[Pubkey],
        signature: &str,
        slot: u64,
        block_time: Option<Timestamp>,
        index: String,
    ) -> Vec<Box<dyn DexEvent>> {
        let program_id = accounts[instruction.program_id_index as usize];
        if !self.should_handle(&program_id) {
            return Vec::new();
        }
        let mut events = Vec::new();
        for (disc, configs) in &self.instruction_configs {
            if instruction.data.len() < disc.len() {
                continue;
            }
            let discriminator = &instruction.data[..disc.len()];
            let data = &instruction.data[disc.len()..];
            if discriminator == disc {
                // 验证账户索引
                if !validate_account_indices(&instruction.accounts, accounts.len()) {
                    continue;
                }

                let account_pubkeys: Vec<Pubkey> = instruction
                    .accounts
                    .iter()
                    .map(|&idx| accounts[idx as usize])
                    .collect();
                for config in configs {
                    if !matches_discriminator(discriminator,config.instruction_discriminator) {
                        continue;
                    }

                    if let Some(event) = self.parse_instruction_event(
                        config,
                        data,
                        &account_pubkeys,
                        signature,
                        slot,
                        block_time,
                        index.clone(),
                    ) {
                        events.push(event);
                    }
                }
            }
        }

        events
    }

    /// 从内联指令中解析事件数据
    fn parse_events_from_inner_instruction(
        &self,
        inner_instruction: &UiCompiledInstruction,
        signature: &str,
        slot: u64,
        block_time: Option<Timestamp>,
        index: String,
    ) -> Vec<Box<dyn DexEvent>> {
        let inner_instruction_data = inner_instruction.data.clone();
        let inner_instruction_data_decoded =
            bs58::decode(inner_instruction_data).into_vec().unwrap();
        if inner_instruction_data_decoded.len() < 16 {
            return Vec::new();
        }
        let inner_instruction_data_decoded_str =
            format!("0x{}", hex::encode(&inner_instruction_data_decoded));
        let data = &inner_instruction_data_decoded[16..];
        let mut events = Vec::new();
        for (disc, configs) in &self.inner_instruction_configs {
            if matches_discriminator_str(&inner_instruction_data_decoded_str, disc) {
                for config in configs {
                    if let Some(event) = self.parse_inner_instruction_event(
                        config,
                        data,
                        signature,
                        slot,
                        block_time,
                        index.clone(),
                    ) {
                        events.push(event);
                    }
                }
            }
        }
        events
    }

    fn protocol(&self) -> ProtocolType {
        self.protocol_type.clone()
    }
}

