use solana_sdk::instruction::CompiledInstruction;
use solana_sdk::pubkey::Pubkey;
use solana_transaction_status::UiCompiledInstruction;
use yellowstone_grpc_proto::prost_types::Timestamp;
use crate::{DexEvent, DexEventType, DexParser, EventMetadata, GenericEventParseConfig,
            GenericEventParser, LiquidityChangeAccounts, ProtocolType,
            RaydiumClmmDecreaseLiquidityV2Event, RaydiumClmmIncreaseLiquidityV2Event,
            RaydiumClmmSwapEvent, RaydiumClmmSwapV2Event};
use crate::utils::{read_u128_le, read_u16_le, read_u64_le, read_u8_le};

/// Raydium CLMM程序ID
pub const RAYDIUM_CLMM_PROGRAM_ID: Pubkey =
    solana_sdk::pubkey!("CAMMCzo5YL8w4VFF8KVHrK22GGUsp5VTaW7grrKgrWqK");

/// 事件鉴别器常量
pub mod discriminators {
    // 指令鉴别器
    pub const SWAP: &[u8] = &[248, 198, 158, 145, 225, 117, 135, 200];
    pub const SWAP_V2: &[u8] = &[43, 4, 237, 11, 26, 201, 30, 98];
    // increaseLiquidity
    pub const INCREASE_LIQUIDITY: &[u8] = & [46, 156, 243, 118, 13, 205, 251, 178];
    // increaseLiquidity v2
    pub const INCREASE_LIQUIDITY_V2: &[u8] = &[133, 29, 89, 223, 69, 238, 176, 10];
    // decreaseLiquidity
    pub const DECREASE_LIQUIDITY: &[u8] = &[160, 38, 208, 111, 104, 91, 44, 1];
    // decreaseLiquidity v2
    pub const DECREASE_LIQUIDITY_V2: &[u8] = &[58, 127, 188, 62, 79, 82, 196, 96];
}


/// Raydium CLMM事件解析器
pub struct RaydiumClmmEventParser {
    inner: GenericEventParser,
}

impl RaydiumClmmEventParser {
    pub fn new() -> Self {
        // 配置所有事件类型
        let configs = vec![
            GenericEventParseConfig {
                inner_instruction_discriminator: "",
                instruction_discriminator: discriminators::SWAP,
                event_type: DexEventType::RaydiumClmmSwap,
                inner_instruction_parser: Self::parse_trade_inner_instruction,
                instruction_parser: Self::parse_swap_instruction,
            },
            GenericEventParseConfig {
                inner_instruction_discriminator: "",
                instruction_discriminator: discriminators::SWAP_V2,
                event_type: DexEventType::RaydiumClmmSwapV2,
                inner_instruction_parser: Self::parse_trade_inner_instruction,
                instruction_parser: Self::parse_swap_v2_instruction,
            },
            GenericEventParseConfig {
                inner_instruction_discriminator: "",
                instruction_discriminator: discriminators::INCREASE_LIQUIDITY_V2,
                event_type: DexEventType::RaydiumClmmIncreaseLiquidityV2,
                inner_instruction_parser: Self::parse_trade_inner_instruction,
                instruction_parser: Self::parse_increase_liquidity_v2_instruction,
            },
            GenericEventParseConfig {
                inner_instruction_discriminator: "",
                instruction_discriminator: discriminators::DECREASE_LIQUIDITY_V2,
                event_type: DexEventType::RaydiumClmmDecreaseLiquidityV2,
                inner_instruction_parser: Self::parse_trade_inner_instruction,
                instruction_parser: Self::parse_decrease_liquidity_v2_instruction,
            }
        ];

        let inner =
            GenericEventParser::new("RaydiumClmmParser".to_string(), RAYDIUM_CLMM_PROGRAM_ID, ProtocolType::RaydiumClmm, configs);

        Self { inner }
    }

    /// 解析交易事件
    fn parse_trade_inner_instruction(
        _data: &[u8],
        _metadata: EventMetadata,
    ) -> Option<Box<dyn DexEvent>> {
        None
    }

    /// 解析交易指令事件
    fn parse_swap_instruction(
        data: &[u8],
        accounts: &[Pubkey],
        metadata: EventMetadata,
    ) -> Option<Box<dyn DexEvent>> {
        if data.len() < 16 || accounts.len() < 10 {
            return None;
        }

        let amount = read_u64_le(data, 0)?;
        let other_amount_threshold = read_u64_le(data, 8)?;
        let sqrt_price_limit_x64 = read_u128_le(data, 16)?;
        let is_base_input = read_u8_le(data, 32)?;

        let mut metadata = metadata;
        metadata.set_id(format!(
            "{}-{}-{}-{}",
            metadata.signature, accounts[2], accounts[3], accounts[4]
        ));

        Some(Box::new(RaydiumClmmSwapEvent {
            metadata,
            amount,
            other_amount_threshold,
            sqrt_price_limit_x64,
            is_base_input: is_base_input == 1,
            payer: accounts[0],
            amm_config: accounts[1],
            pool_state: accounts[2],
            input_token_account: accounts[3],
            output_token_account: accounts[4],
            input_vault: accounts[5],
            output_vault: accounts[6],
            observation_state: accounts[7],
            token_program: accounts[8],
            tick_array: accounts[9],
            remaining_accounts: accounts[10..].to_vec(),
            ..Default::default()
        }))
    }

    fn parse_swap_v2_instruction(
        data: &[u8],
        accounts: &[Pubkey],
        metadata: EventMetadata,
    ) -> Option<Box<dyn DexEvent>> {
        if data.len() < 16 || accounts.len() < 13 {
            return None;
        }

        let amount = read_u64_le(data, 0)?;
        let other_amount_threshold = read_u64_le(data, 8)?;
        let sqrt_price_limit_x64 = read_u128_le(data, 16)?;
        let is_base_input = read_u8_le(data, 32)?;

        let mut metadata = metadata;
        metadata.set_id(format!(
            "{}-{}-{}-{}",
            metadata.signature, accounts[2], accounts[3], accounts[4]
        ));

        Some(Box::new(RaydiumClmmSwapV2Event {
            metadata,
            amount,
            other_amount_threshold,
            sqrt_price_limit_x64,
            is_base_input: is_base_input == 1,
            payer: accounts[0],
            amm_config: accounts[1],
            pool_state: accounts[2],
            input_token_account: accounts[3],
            output_token_account: accounts[4],
            input_vault: accounts[5],
            output_vault: accounts[6],
            observation_state: accounts[7],
            token_program: accounts[8],
            token_program2022: accounts[9],
            memo_program: accounts[10],
            input_vault_mint: accounts[11],
            output_vault_mint: accounts[12],
            remaining_accounts: accounts[13..].to_vec(),
            ..Default::default()
        }))
    }

    fn parse_increase_liquidity_v2_instruction(
        data: &[u8],
        accounts: &[Pubkey],
        metadata: EventMetadata
    ) -> Option<Box<dyn DexEvent>> {
        if data.len() < 34 || accounts.len() < 15 {
            return None;
        }
        let liquidity = read_u128_le(data, 0)?;
        let amount_0_max = read_u64_le(data, 16)?;
        let amount_1_max = read_u64_le(data, 24)?;
        let base_flag = read_u16_le(data, 32)?;

        let base_flag = if base_flag == 0 {
            None
        } else {
            Some(base_flag == 1)
        };

        let mut metadata = metadata;
        metadata.set_id(format!(
            "{}-{}-{}-{}",
            metadata.signature, accounts[2], accounts[3], accounts[4]
        ));

        Some(Box::new(RaydiumClmmIncreaseLiquidityV2Event {
            metadata,
            liquidity,
            amount_0_max,
            amount_1_max,
            base_flag,
            accounts: LiquidityChangeAccounts {
                nft_owner: accounts[0],
                nft_account: accounts[1],
                pool_state: accounts[2],
                protocol_position: accounts[3],
                personal_position: accounts[4],
                tick_array_lower: accounts[5],
                tick_array_upper: accounts[6],
                token_account_0: accounts[7],
                token_account_1: accounts[8],
                token_vault_0: accounts[9],
                token_vault_1: accounts[10],
                token_program: accounts[11],
                token_program2022: accounts[12],
                vault_0_mint: accounts[13],
                vault_1_mint: accounts[14],
            }
        }))
    }

    fn parse_decrease_liquidity_v2_instruction(
        data: &[u8],
        accounts: &[Pubkey],
        metadata: EventMetadata
    ) -> Option<Box<dyn DexEvent>> {
        if data.len() < 32 || accounts.len() < 15 {
            return None;
        }
        let liquidity = read_u128_le(data, 0)?;
        let amount_0_min = read_u64_le(data, 16)?;
        let amount_1_min = read_u64_le(data, 24)?;

        let mut metadata = metadata;
        metadata.set_id(format!(
            "{}-{}-{}-{}",
            metadata.signature, accounts[2], accounts[3], accounts[4]
        ));

        Some(Box::new(RaydiumClmmDecreaseLiquidityV2Event {
            metadata,
            liquidity,
            amount_0_min,
            amount_1_min,
            accounts: LiquidityChangeAccounts {
                nft_owner: accounts[0],
                nft_account: accounts[1],
                pool_state: accounts[2],
                protocol_position: accounts[3],
                personal_position: accounts[4],
                tick_array_lower: accounts[5],
                tick_array_upper: accounts[6],
                token_account_0: accounts[7],
                token_account_1: accounts[8],
                token_vault_0: accounts[9],
                token_vault_1: accounts[10],
                token_program: accounts[11],
                token_program2022: accounts[12],
                vault_0_mint: accounts[13],
                vault_1_mint: accounts[14],
            }
        }))
    }
}




#[async_trait::async_trait]
impl DexParser for RaydiumClmmEventParser {
    fn name(&self) -> &str {
        self.inner.name()
    }

    fn supported_program_ids(&self) -> Vec<Pubkey> {
        self.inner.supported_program_ids()
    }
    fn should_handle(&self, program_id: &Pubkey) -> bool {
        self.inner.should_handle(program_id)
    }

    fn parse_events_from_instruction(
        &self,
        instruction: &CompiledInstruction,
        accounts: &[Pubkey],
        signature: &str,
        slot: u64,
        block_time: Option<Timestamp>,
        index: String,
    ) -> Vec<Box<dyn DexEvent>> {
        self.inner.parse_events_from_instruction(
            instruction,
            accounts,
            signature,
            slot,
            block_time,
            index,
        )
    }

    fn parse_events_from_inner_instruction(
        &self,
        inner_instruction: &UiCompiledInstruction,
        signature: &str,
        slot: u64,
        block_time: Option<Timestamp>,
        index: String,
    ) -> Vec<Box<dyn DexEvent>> {
        self.inner.parse_events_from_inner_instruction(
            inner_instruction,
            signature,
            slot,
            block_time,
            index,
        )
    }

    fn protocol(&self) -> ProtocolType {
        self.inner.protocol()
    }
}
