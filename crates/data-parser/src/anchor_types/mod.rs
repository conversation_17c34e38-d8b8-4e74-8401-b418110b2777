//! Anchor 类型定义模块
//!
//! 基于 IDL 文件生成的 Anchor 类型定义，用于替换手动解析

pub mod raydium;
pub mod meteora;

// 重新导出，避免名称冲突
pub use raydium::{
    RAYDIUM_CLMM_PROGRAM_ID, PoolState, TickArrayState, TickArrayBitmapExtension,
    ObservationState, PersonalPositionState, ProtocolPositionState, TickState, Observation,
    RewardInfo as RaydiumRewardInfo
};
pub use meteora::{
    METEORA_DLMM_PROGRAM_ID, LbPair, BinArray, BinArrayBitmapExtension,
    Oracle, Position, PositionV2, StaticParameters, VariableParameters, Bin,
    RewardInfo as MeteoraRewardInfo, UserRewardInfo, FeeInfo
};
