//! Raydium CLMM Anchor 类型定义
//!
//! 基于 raydium_clmm.json IDL 文件生成的类型定义

use anchor_lang::prelude::*;

/// Raydium CLMM 程序ID
pub const RAYDIUM_CLMM_PROGRAM_ID: Pubkey =
    solana_sdk::pubkey!("CAMMCzo5YL8w4VFF8KVHrK22GGUsp5VTaW7grrKgrWqK");

/// 奖励信息结构
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub struct RewardInfo {
    /// 奖励状态 (0=未激活, 1=激活, 2=结束)
    pub reward_state: u8,
    /// 填充字节 (7字节对齐)
    pub padding_u8: [u8; 7],
    /// 开始时间
    pub open_time: u64,
    /// 结束时间
    pub end_time: u64,
    /// 最后更新时间
    pub last_update_time: u64,
    /// 每秒排放量（X64格式）
    pub emissions_per_second_x64: u128,
    /// 总排放量
    pub reward_total_emissioned: u64,
    /// 已领取奖励
    pub reward_claimed: u64,
    /// 代币mint地址
    pub token_mint: Pubkey,
    /// 代币金库
    pub token_vault: Pubkey,
    /// 权限账户
    pub authority: Pubkey,
    /// 奖励增长全局X64
    pub reward_growth_global_x64: u128,
}

/// Tick状态
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub struct TickState {
    /// 该tick的net流动性
    pub liquidity_net: i128,
    /// 该tick的gross流动性
    pub liquidity_gross: u128,
    /// 费用增长外部0 X64
    pub fee_growth_outside_0_x64: u128,
    /// 费用增长外部1 X64
    pub fee_growth_outside_1_x64: u128,
    /// 奖励增长外部数组
    pub reward_growths_outside_x64: [u128; 3],
    /// 填充字段
    pub padding: [u64; 13],
}

/// 观察数据
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub struct Observation {
    /// 区块时间戳
    pub block_timestamp: u32,
    /// 累积tick
    pub tick_cumulative: i64,
    /// 填充字段
    pub padding: [u64; 4],
}

/// Raydium CLMM 池状态
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub struct PoolState {
    /// Bump种子
    pub bump: [u8; 1],
    /// AMM配置
    pub amm_config: Pubkey,
    /// 池所有者
    pub owner: Pubkey,
    /// 代币0的mint
    pub token_mint_0: Pubkey,
    /// 代币1的mint
    pub token_mint_1: Pubkey,
    /// 代币0的金库
    pub token_vault_0: Pubkey,
    /// 代币1的金库
    pub token_vault_1: Pubkey,
    /// 观察状态key
    pub observation_key: Pubkey,
    /// 代币0精度
    pub mint_decimals_0: u8,
    /// 代币1精度
    pub mint_decimals_1: u8,
    /// Tick间距
    pub tick_spacing: u16,
    /// 流动性
    pub liquidity: u128,
    /// 价格平方根 X64
    pub sqrt_price_x64: u128,
    /// 当前tick
    pub tick_current: i32,
    /// 填充字段3
    pub padding3: [u16; 1],
    /// 填充字段4
    pub padding4: [u16; 1],
    /// 费用增长全局0 X64
    pub fee_growth_global_0_x64: u128,
    /// 费用增长全局1 X64
    pub fee_growth_global_1_x64: u128,
    /// 协议费用代币0
    pub protocol_fees_token_0: u64,
    /// 协议费用代币1
    pub protocol_fees_token_1: u64,
    /// 交换输入金额代币0
    pub swap_in_amount_token_0: u128,
    /// 交换输出金额代币1
    pub swap_out_amount_token_1: u128,
    /// 交换输入金额代币1
    pub swap_in_amount_token_1: u128,
    /// 交换输出金额代币0
    pub swap_out_amount_token_0: u128,
    /// 状态
    pub status: u8,
    /// 填充字段
    pub padding: [u8; 7],
    /// 奖励信息数组（最多3个）
    pub reward_infos: [RewardInfo; 3],
    /// Tick数组位图
    pub tick_array_bitmap: [u64; 16],
    /// 总费用代币0
    pub total_fees_token_0: u64,
    /// 已领取总费用代币0
    pub total_fees_claimed_token_0: u64,
    /// 总费用代币1
    pub total_fees_token_1: u64,
    /// 已领取总费用代币1
    pub total_fees_claimed_token_1: u64,
    /// 基金费用代币0
    pub fund_fees_token_0: u64,
    /// 基金费用代币1
    pub fund_fees_token_1: u64,
    /// 开放时间
    pub open_time: u64,
    /// 最近epoch
    pub recent_epoch: u64,
    /// 填充字段1
    pub padding1: [u64; 24],
    /// 填充字段2
    pub padding2: [u64; 32],
}

/// Raydium Tick Array 状态
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub struct TickArrayState {
    /// 池ID
    pub pool_id: Pubkey,
    /// 开始tick索引
    pub start_tick_index: i32,
    /// Ticks数组（60个tick）
    pub ticks: [TickState; 60],
    /// 初始化的tick数量
    pub initialized_tick_count: u8,
    /// 最近epoch
    pub recent_epoch: u64,
    /// 填充字段
    pub padding: [u8; 107],
}

/// Raydium Tick Array Bitmap Extension
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub struct TickArrayBitmapExtension {
    /// 池状态地址
    pub pool_id: Pubkey,
    /// 正向位图（1024个u64）
    pub positive_tick_array_bitmap: [[u64; 8]; 128],
    /// 负向位图（1024个u64）
    pub negative_tick_array_bitmap: [[u64; 8]; 128],
}

/// Raydium 观察状态
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub struct ObservationState {
    /// 是否初始化
    pub initialized: bool,
    /// 最近epoch
    pub recent_epoch: u64,
    /// 观察数组索引
    pub observation_index: u16,
    /// 池状态地址
    pub pool_id: Pubkey,
    /// 观察数据数组
    pub observations: [Observation; 100],
    /// 填充字段
    pub padding: [u64; 4],
}

/// Raydium 个人仓位
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub struct PersonalPositionState {
    /// Bump种子
    pub bump: [u8; 1],
    /// NFT mint
    pub nft_mint: Pubkey,
    /// 池状态地址
    pub pool_id: Pubkey,
    /// 下限tick
    pub tick_lower_index: i32,
    /// 上限tick
    pub tick_upper_index: i32,
    /// 流动性
    pub liquidity: u128,
    /// 费用增长内部0 X64
    pub fee_growth_inside_0_last_x64: u128,
    /// 费用增长内部1 X64
    pub fee_growth_inside_1_last_x64: u128,
    /// 代币欠费0
    pub token_fees_owed_0: u64,
    /// 代币欠费1
    pub token_fees_owed_1: u64,
    /// 奖励增长内部最后X64数组
    pub reward_growth_inside_last_x64: [u128; 3],
    /// 奖励欠费数组
    pub reward_amounts_owed: [u64; 3],
}

/// Raydium 协议仓位
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub struct ProtocolPositionState {
    /// Bump种子
    pub bump: [u8; 1],
    /// 池状态地址
    pub pool_id: Pubkey,
    /// 下限tick
    pub tick_lower_index: i32,
    /// 上限tick
    pub tick_upper_index: i32,
    /// 流动性
    pub liquidity: u128,
    /// 费用增长内部0 X64
    pub fee_growth_inside_0_last_x64: u128,
    /// 费用增长内部1 X64
    pub fee_growth_inside_1_last_x64: u128,
    /// 代币欠费0
    pub token_fees_owed_0: u64,
    /// 代币欠费1
    pub token_fees_owed_1: u64,
    /// 奖励增长内部最后X64数组
    pub reward_growth_inside_last_x64: [u128; 3],
    /// 奖励欠费数组
    pub reward_amounts_owed: [u64; 3],
}
