//! Anchor 解析器管理器
//!
//! 统一管理所有 Anchor 解析器，提供统一的接口

use async_trait::async_trait;
use solana_sdk::pubkey::Pubkey;
use shared::Result;
use std::sync::Arc;

use crate::accounts::traits::{
    AccountParser, AccountType, ParsedAccount
};
use crate::accounts::raydium::RaydiumAnchorAccountParser;
use crate::accounts::meteora::MeteoraAnchorAccountParser;

/// Anchor 解析器管理器
pub struct AnchorParserManager {
    name: String,
    parsers: Vec<Arc<dyn AccountParser>>,
}

impl AnchorParserManager {
    /// 创建新的 Anchor 解析器管理器
    pub fn new() -> Self {
        let parsers: Vec<Arc<dyn AccountParser>> = vec![
            Arc::new(RaydiumAnchorAccountParser::new()),
            Arc::new(MeteoraAnchorAccountParser::new()),
        ];

        Self {
            name: "AnchorParserManager".to_string(),
            parsers,
        }
    }

    /// 添加解析器
    pub fn add_parser(&mut self, parser: Arc<dyn AccountParser>) {
        self.parsers.push(parser);
    }

    /// 获取所有支持的解析器
    fn get_parsers(&self) -> &[Arc<dyn AccountParser>] {
        &self.parsers
    }

    /// 根据程序ID选择合适的解析器
    fn select_parser(&self, program_id: &Pubkey) -> Option<&Arc<dyn AccountParser>> {
        self.parsers.iter().find(|parser| {
            parser.supported_program_ids().contains(program_id)
        })
    }

    /// 根据程序ID和数据选择最佳解析器
    fn select_best_parser(&self, program_id: &Pubkey, account_data: &[u8]) -> Option<&Arc<dyn AccountParser>> {
        self.parsers.iter().find(|parser| {
            parser.can_parse(program_id, account_data)
        })
    }
}

impl Default for AnchorParserManager {
    fn default() -> Self {
        Self::new()
    }
}

#[async_trait]
impl AccountParser for AnchorParserManager {
    fn name(&self) -> &str {
        &self.name
    }

    fn supported_program_ids(&self) -> Vec<Pubkey> {
        self.parsers
            .iter()
            .flat_map(|parser| parser.supported_program_ids())
            .collect()
    }

    fn supported_account_types(&self) -> Vec<AccountType> {
        self.parsers
            .iter()
            .flat_map(|parser| parser.supported_account_types())
            .collect()
    }

    fn can_parse(&self, program_id: &Pubkey, account_data: &[u8]) -> bool {
        self.select_best_parser(program_id, account_data).is_some()
    }

    fn identify_account_type(&self, program_id: &Pubkey, account_data: &[u8]) -> Option<AccountType> {
        self.select_best_parser(program_id, account_data)?
            .identify_account_type(program_id, account_data)
    }

    async fn parse_account(
        &self,
        address: Pubkey,
        program_id: Pubkey,
        account_data: &[u8],
    ) -> Result<ParsedAccount> {
        if let Some(parser) = self.select_best_parser(&program_id, account_data) {
            parser.parse_account(address, program_id, account_data).await
        } else {
            Err(shared::EchoesError::Parse(
                format!("No parser available for program ID: {}", program_id)
            ))
        }
    }
}

/// 创建默认的 Anchor 解析器管理器实例
pub fn create_anchor_parser_manager() -> AnchorParserManager {
    AnchorParserManager::new()
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::anchor_types::raydium::RAYDIUM_CLMM_PROGRAM_ID;
    use crate::anchor_types::meteora::METEORA_DLMM_PROGRAM_ID;

    #[test]
    fn test_anchor_parser_manager_creation() {
        let manager = AnchorParserManager::new();
        assert_eq!(manager.name(), "AnchorParserManager");
    }

    #[test]
    fn test_supported_program_ids() {
        let manager = AnchorParserManager::new();
        let program_ids = manager.supported_program_ids();

        assert!(program_ids.contains(&RAYDIUM_CLMM_PROGRAM_ID));
        assert!(program_ids.contains(&METEORA_DLMM_PROGRAM_ID));
    }

    #[test]
    fn test_parser_selection() {
        let manager = AnchorParserManager::new();

        // 测试 Raydium 解析器选择
        let raydium_parser = manager.select_parser(&RAYDIUM_CLMM_PROGRAM_ID);
        assert!(raydium_parser.is_some());

        // 测试 Meteora 解析器选择
        let meteora_parser = manager.select_parser(&METEORA_DLMM_PROGRAM_ID);
        assert!(meteora_parser.is_some());

        // 测试不支持的程序ID
        let unknown_program_id = Pubkey::new_unique();
        let unknown_parser = manager.select_parser(&unknown_program_id);
        assert!(unknown_parser.is_none());
    }

    #[test]
    fn test_supported_account_types() {
        let manager = AnchorParserManager::new();
        let account_types = manager.supported_account_types();

        // 验证包含 Raydium 账号类型
        assert!(account_types.contains(&AccountType::RaydiumPoolState));
        assert!(account_types.contains(&AccountType::RaydiumTickArrayState));
        assert!(account_types.contains(&AccountType::RaydiumPersonalPosition));

        // 验证包含 Meteora 账号类型
        assert!(account_types.contains(&AccountType::MeteoraLbPair));
        assert!(account_types.contains(&AccountType::MeteoraBinArray));
        assert!(account_types.contains(&AccountType::MeteoraPosition));
        assert!(account_types.contains(&AccountType::MeteoraPositionV2));
    }
}
