//! 状态增量更新机制
//!
//! 提供基于DEX事件的状态增量更新功能

use crate::pool::{PoolState, PoolStateCache, PoolType, TokenReserve, ClmmPoolState, CpmmPoolState, PumpFunBondingCurveState, PoolSpecificState};
use crate::raydium_clmm::{RaydiumClmmPoolState, RaydiumClmmPoolManager, TickArrayState, TickArrayBitmapExtension};
use data_parser::events::{DexEvent, DexEventType, RaydiumClmmSwapEvent, RaydiumClmmSwapV2Event, PumpFunTradeEvent, RaydiumCpmmSwapBaseInputEvent, RaydiumCpmmSwapBaseOutputEvent};
use shared::{EchoesError, Result};
use solana_sdk::pubkey::Pubkey;
use tracing::{info, warn, debug};

/// 状态更新器 - 负责基于DEX事件更新池状态
pub struct StateUpdater {
    pool_cache: PoolStateCache,
}

impl StateUpdater {
    /// 创建新的状态更新器
    pub fn new(pool_cache: PoolStateCache) -> Self {
        Self { pool_cache }
    }

    /// 处理DEX事件并更新相关池状态
    pub async fn process_dex_event(&self, event: Box<dyn DexEvent>) -> Result<()> {
        debug!("Processing DEX event: {:?}", event.event_type());
        
        match event.event_type() {
            DexEventType::RaydiumClmmSwap => {
                if let Some(swap_event) = event.as_any().downcast_ref::<RaydiumClmmSwapEvent>() {
                    self.update_raydium_clmm_swap(swap_event).await?;
                }
            }
            DexEventType::RaydiumClmmSwapV2 => {
                if let Some(swap_event) = event.as_any().downcast_ref::<RaydiumClmmSwapV2Event>() {
                    self.update_raydium_clmm_v2_swap(swap_event).await?;
                }
            }
            DexEventType::RaydiumCpmmSwapBaseInput => {
                if let Some(swap_event) = event.as_any().downcast_ref::<RaydiumCpmmSwapBaseInputEvent>() {
                    self.update_raydium_cpmm_swap(swap_event).await?;
                }
            }
            DexEventType::RaydiumCpmmSwapBaseOutput => {
                if let Some(swap_event) = event.as_any().downcast_ref::<RaydiumCpmmSwapBaseOutputEvent>() {
                    self.update_raydium_cpmm_swap_output(swap_event).await?;
                }
            }
            DexEventType::PumpFunBuy | DexEventType::PumpFunSell => {
                if let Some(trade_event) = event.as_any().downcast_ref::<PumpFunTradeEvent>() {
                    self.update_pumpfun_trade(trade_event).await?;
                }
            }
            _ => {
                debug!("Unhandled event type: {:?}", event.event_type());
            }
        }

        Ok(())
    }

    /// 更新Raydium CLMM池状态（基于交换事件）
    async fn update_raydium_clmm_swap(&self, event: &RaydiumClmmSwapEvent) -> Result<()> {
        let pool_address = event.pool_state;
        
        // 获取现有池状态或创建新的
        let mut pool_state = self.pool_cache.get_pool(&pool_address)
            .unwrap_or_else(|| self.create_default_clmm_pool(pool_address));

        // 从转账数据中提取储备变化
        if let Some((token_a_change, token_b_change)) = self.extract_reserve_changes(event.get_transfer_datas()) {
            let new_token_a = (pool_state.token_a.amount as i64 + token_a_change).max(0) as u64;
            let new_token_b = (pool_state.token_b.amount as i64 + token_b_change).max(0) as u64;
            
            pool_state.update_reserves(new_token_a, new_token_b, event.slot());
            
            // 更新CLMM特定状态
            if let PoolSpecificState::Clmm(ref mut clmm_state) = pool_state.specific_state {
                clmm_state.sqrt_price_x64 = event.sqrt_price_limit_x64;
                // 在实际实现中，这里需要从账户数据中获取真实的价格
            }
        }

        self.pool_cache.upsert_pool(pool_state)?;
        info!("Updated Raydium CLMM pool {}", pool_address);
        
        Ok(())
    }

    /// 更新Raydium CLMM V2池状态
    async fn update_raydium_clmm_v2_swap(&self, event: &RaydiumClmmSwapV2Event) -> Result<()> {
        let pool_address = event.pool_state;
        
        let mut pool_state = self.pool_cache.get_pool(&pool_address)
            .unwrap_or_else(|| self.create_default_clmm_pool(pool_address));

        if let Some((token_a_change, token_b_change)) = self.extract_reserve_changes(event.get_transfer_datas()) {
            let new_token_a = (pool_state.token_a.amount as i64 + token_a_change).max(0) as u64;
            let new_token_b = (pool_state.token_b.amount as i64 + token_b_change).max(0) as u64;
            
            pool_state.update_reserves(new_token_a, new_token_b, event.slot());
            
            if let PoolSpecificState::Clmm(ref mut clmm_state) = pool_state.specific_state {
                clmm_state.sqrt_price_x64 = event.sqrt_price_limit_x64;
            }
        }

        self.pool_cache.upsert_pool(pool_state)?;
        info!("Updated Raydium CLMM V2 pool {}", pool_address);
        
        Ok(())
    }

    /// 更新Raydium CPMM池状态
    async fn update_raydium_cpmm_swap(&self, event: &RaydiumCpmmSwapBaseInputEvent) -> Result<()> {
        let pool_address = event.pool_id;
        
        let mut pool_state = self.pool_cache.get_pool(&pool_address)
            .unwrap_or_else(|| self.create_default_cpmm_pool(pool_address));

        if let Some((token_a_change, token_b_change)) = self.extract_reserve_changes(event.get_transfer_datas()) {
            let new_token_a = (pool_state.token_a.amount as i64 + token_a_change).max(0) as u64;
            let new_token_b = (pool_state.token_b.amount as i64 + token_b_change).max(0) as u64;
            
            pool_state.update_reserves(new_token_a, new_token_b, event.slot());
            
            // 更新K值
            if let PoolSpecificState::Cpmm(ref mut cpmm_state) = pool_state.specific_state {
                cpmm_state.k_value = (new_token_a as u128) * (new_token_b as u128);
            }
        }

        self.pool_cache.upsert_pool(pool_state)?;
        info!("Updated Raydium CPMM pool {}", pool_address);
        
        Ok(())
    }

    /// 更新Raydium CPMM池状态（输出版本）
    async fn update_raydium_cpmm_swap_output(&self, event: &RaydiumCpmmSwapBaseOutputEvent) -> Result<()> {
        let pool_address = event.pool_id;
        
        let mut pool_state = self.pool_cache.get_pool(&pool_address)
            .unwrap_or_else(|| self.create_default_cpmm_pool(pool_address));

        if let Some((token_a_change, token_b_change)) = self.extract_reserve_changes(event.get_transfer_datas()) {
            let new_token_a = (pool_state.token_a.amount as i64 + token_a_change).max(0) as u64;
            let new_token_b = (pool_state.token_b.amount as i64 + token_b_change).max(0) as u64;
            
            pool_state.update_reserves(new_token_a, new_token_b, event.slot());
            
            if let PoolSpecificState::Cpmm(ref mut cpmm_state) = pool_state.specific_state {
                cpmm_state.k_value = (new_token_a as u128) * (new_token_b as u128);
            }
        }

        self.pool_cache.upsert_pool(pool_state)?;
        info!("Updated Raydium CPMM pool (output) {}", pool_address);
        
        Ok(())
    }

    /// 更新PumpFun池状态
    async fn update_pumpfun_trade(&self, event: &PumpFunTradeEvent) -> Result<()> {
        let pool_address = event.bonding_curve;
        
        let mut pool_state = self.pool_cache.get_pool(&pool_address)
            .unwrap_or_else(|| self.create_default_pumpfun_pool(pool_address, event.mint));

        // 更新储备
        pool_state.update_reserves(event.real_sol_reserves, event.real_token_reserves, event.slot());
        
        // 更新PumpFun特定状态
        if let PoolSpecificState::PumpFun(ref mut pf_state) = pool_state.specific_state {
            pf_state.virtual_sol_reserves = event.virtual_sol_reserves;
            pf_state.virtual_token_reserves = event.virtual_token_reserves;
            pf_state.real_sol_reserves = event.real_sol_reserves;
            pf_state.real_token_reserves = event.real_token_reserves;
        }

        self.pool_cache.upsert_pool(pool_state)?;
        info!("Updated PumpFun pool {}", pool_address);
        
        Ok(())
    }

    /// 从转账数据中提取储备变化
    fn extract_reserve_changes(&self, transfer_datas: &[data_parser::events::TransferData]) -> Option<(i64, i64)> {
        if transfer_datas.len() < 2 {
            return None;
        }
        
        // 简化的实现：假设前两个转账分别对应token A和token B
        let token_a_change = transfer_datas[0].amount as i64;
        let token_b_change = -(transfer_datas[1].amount as i64); // 输出为负值
        
        Some((token_a_change, token_b_change))
    }

    /// 创建默认的CLMM池状态
    fn create_default_clmm_pool(&self, address: Pubkey) -> PoolState {
        let token_a = TokenReserve {
            mint: Pubkey::new_unique(),
            amount: 0,
            decimals: 6,
            symbol: None,
        };
        
        let token_b = TokenReserve {
            mint: Pubkey::new_unique(),
            amount: 0,
            decimals: 9,
            symbol: None,
        };

        let clmm_state = ClmmPoolState {
            sqrt_price_x64: 0,
            tick_current: 0,
            observation_index: 0,
            liquidity: 0,
            tick_spacing: 64,
            fee_rate: 3000, // 0.3%
            protocol_fee_rate: 120, // 1.2%
            fee_growth_global_0_x64: 0,
            fee_growth_global_1_x64: 0,
            tick_array_bitmap: [0; 16],
            protocol_fees_token_0: 0,
            protocol_fees_token_1: 0,
            total_fees_token_0: 0,
            total_fees_token_1: 0,
            total_fees_claimed_token_0: 0,
            total_fees_claimed_token_1: 0,
            reward_infos: Vec::new(),
            open_time: 0,
            recent_epoch: 0,
            raydium_manager: None,
        };

        PoolState::new(
            address,
            PoolType::RaydiumClmm,
            token_a,
            token_b,
            PoolSpecificState::Clmm(clmm_state),
        )
    }

    /// 创建默认的CPMM池状态
    fn create_default_cpmm_pool(&self, address: Pubkey) -> PoolState {
        let token_a = TokenReserve {
            mint: Pubkey::new_unique(),
            amount: 0,
            decimals: 6,
            symbol: None,
        };
        
        let token_b = TokenReserve {
            mint: Pubkey::new_unique(),
            amount: 0,
            decimals: 9,
            symbol: None,
        };

        let cpmm_state = CpmmPoolState {
            k_value: 0,
            fee_rate: 30, // 0.3%
            lp_supply: 0,
        };

        PoolState::new(
            address,
            PoolType::RaydiumCpmm,
            token_a,
            token_b,
            PoolSpecificState::Cpmm(cpmm_state),
        )
    }

    /// 创建默认的PumpFun池状态
    fn create_default_pumpfun_pool(&self, address: Pubkey, mint: Pubkey) -> PoolState {
        let token_a = TokenReserve {
            mint: solana_sdk::system_program::id(), // 使用系统程序ID作为SOL的占位符
            amount: 0,
            decimals: 9,
            symbol: Some("SOL".to_string()),
        };
        
        let token_b = TokenReserve {
            mint,
            amount: 0,
            decimals: 6,
            symbol: None,
        };

        let pf_state = PumpFunBondingCurveState {
            virtual_sol_reserves: 0,
            virtual_token_reserves: 0,
            real_sol_reserves: 0,
            real_token_reserves: 0,
            token_total_supply: 0,
            complete: false,
        };

        PoolState::new(
            address,
            PoolType::PumpFun,
            token_a,
            token_b,
            PoolSpecificState::PumpFun(pf_state),
        )
    }

    /// 批量处理事件
    pub async fn process_batch_events(&self, events: Vec<Box<dyn DexEvent>>) -> Result<usize> {
        let mut processed_count = 0;
        
        for event in events {
            match self.process_dex_event(event).await {
                Ok(()) => processed_count += 1,
                Err(e) => {
                    warn!("Failed to process event: {}", e);
                }
            }
        }

        info!("Processed {} events in batch", processed_count);
        Ok(processed_count)
    }

    /// 获取池状态缓存的引用
    pub fn get_pool_cache(&self) -> &PoolStateCache {
        &self.pool_cache
    }

    /// 从JSON文件解析并创建Raydium CLMM池状态
    pub async fn load_raydium_clmm_from_json(
        &self,
        pool_address: Pubkey,
        pool_json_path: &str,
        tick_array_json_path: Option<&str>,
        bitmap_extension_json_path: Option<&str>,
    ) -> Result<()> {
        // 读取池状态JSON
        let pool_json = tokio::fs::read_to_string(pool_json_path).await
            .map_err(|e| EchoesError::Io(format!("Failed to read pool JSON: {}", e)))?;
        
        let pool_value: serde_json::Value = serde_json::from_str(&pool_json)
            .map_err(|e| EchoesError::Parsing(format!("Failed to parse pool JSON: {}", e)))?;

        let mut raydium_state = RaydiumClmmPoolState::from_json_value(&pool_value)?;
        raydium_state.pool_id = pool_address;

        // 创建池管理器
        let mut manager = RaydiumClmmPoolManager::new(raydium_state.clone());

        // 如果提供了tick数组JSON，加载它
        if let Some(tick_path) = tick_array_json_path {
            let tick_json = tokio::fs::read_to_string(tick_path).await
                .map_err(|e| EchoesError::Io(format!("Failed to read tick array JSON: {}", e)))?;
            
            let tick_value: serde_json::Value = serde_json::from_str(&tick_json)
                .map_err(|e| EchoesError::Parsing(format!("Failed to parse tick array JSON: {}", e)))?;
            
            let tick_array = TickArrayState::from_json_value(&tick_value)?;
            manager.upsert_tick_array(tick_array);
        }

        // 如果提供了位图扩展JSON，加载它
        if let Some(bitmap_path) = bitmap_extension_json_path {
            let bitmap_json = tokio::fs::read_to_string(bitmap_path).await
                .map_err(|e| EchoesError::Io(format!("Failed to read bitmap extension JSON: {}", e)))?;
            
            let bitmap_value: serde_json::Value = serde_json::from_str(&bitmap_json)
                .map_err(|e| EchoesError::Parsing(format!("Failed to parse bitmap extension JSON: {}", e)))?;
            
            let bitmap_ext = TickArrayBitmapExtension::from_json_value(&bitmap_value)?;
            manager.set_bitmap_extension(bitmap_ext);
        }

        // 创建PoolState
        let pool_state = PoolState::from_raydium_clmm(pool_address, raydium_state)?;
        
        // 存储到缓存中
        self.pool_cache.upsert_pool(pool_state)?;
        
        info!("Loaded Raydium CLMM pool {} from JSON files", pool_address);
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use data_parser::events::{EventMetadata, ProtocolType, TransferData};
    use solana_sdk::pubkey::Pubkey;

    #[tokio::test]
    async fn test_state_updater_creation() {
        let cache = PoolStateCache::new();
        let updater = StateUpdater::new(cache);
        
        // 验证初始状态
        let stats = updater.get_pool_cache().get_stats();
        assert_eq!(stats.total_pools, 0);
    }

    #[tokio::test]
    async fn test_pumpfun_trade_update() {
        let cache = PoolStateCache::new();
        let updater = StateUpdater::new(cache);
        
        let mut trade_event = PumpFunTradeEvent {
            metadata: EventMetadata::default(),
            mint: Pubkey::new_unique(),
            bonding_curve: Pubkey::new_unique(),
            user: Pubkey::new_unique(),
            is_buy: true,
            amount: 1000000,
            sol_amount: 500000,
            virtual_sol_reserves: 30000000000,
            virtual_token_reserves: 1073000000000000,
            real_sol_reserves: 1500000,
            real_token_reserves: 793000000000,
        };

        trade_event.metadata.slot = 12345;
        trade_event.metadata.event_type = DexEventType::PumpFunBuy;

        // 处理事件
        let event: Box<dyn DexEvent> = Box::new(trade_event.clone());
        updater.process_dex_event(event).await.unwrap();
        
        // 验证池状态已更新
        let pool_state = updater.get_pool_cache().get_pool(&trade_event.bonding_curve).unwrap();
        assert_eq!(pool_state.last_slot, 12345);
        assert_eq!(pool_state.token_a.amount, trade_event.real_sol_reserves);
        assert_eq!(pool_state.token_b.amount, trade_event.real_token_reserves);
        
        // 验证PumpFun特定状态
        if let PoolSpecificState::PumpFun(pf_state) = pool_state.specific_state {
            assert_eq!(pf_state.virtual_sol_reserves, trade_event.virtual_sol_reserves);
            assert_eq!(pf_state.virtual_token_reserves, trade_event.virtual_token_reserves);
        } else {
            panic!("Expected PumpFun pool state");
        }
    }

    #[tokio::test]
    async fn test_batch_event_processing() {
        let cache = PoolStateCache::new();
        let updater = StateUpdater::new(cache);
        
        let mut events: Vec<Box<dyn DexEvent>> = Vec::new();
        
        // 创建多个PumpFun交易事件
        for i in 0..5 {
            let mut trade_event = PumpFunTradeEvent {
                metadata: EventMetadata::default(),
                mint: Pubkey::new_unique(),
                bonding_curve: Pubkey::new_unique(),
                user: Pubkey::new_unique(),
                is_buy: i % 2 == 0,
                amount: 1000000 + i * 100000,
                sol_amount: 500000 + i * 50000,
                virtual_sol_reserves: 30000000000,
                virtual_token_reserves: 1073000000000000,
                real_sol_reserves: 1500000 + i * 100000,
                real_token_reserves: 793000000000,
            };
            
            trade_event.metadata.slot = 12345 + i;
            trade_event.metadata.event_type = if i % 2 == 0 { DexEventType::PumpFunBuy } else { DexEventType::PumpFunSell };
            
            events.push(Box::new(trade_event));
        }
        
        // 批量处理事件
        let processed_count = updater.process_batch_events(events).await.unwrap();
        assert_eq!(processed_count, 5);
        
        // 验证缓存中有5个池
        let stats = updater.get_pool_cache().get_stats();
        assert_eq!(stats.total_pools, 5);
    }
}