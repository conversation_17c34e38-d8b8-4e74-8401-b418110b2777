//! Meteora DLMM 池状态实现
//!
//! 实现 Meteora DLMM 特定的池状态逻辑

use super::types::*;
use super::types::utils;
use serde::{Deserialize, Serialize};
use solana_sdk::pubkey::Pubkey;
use std::collections::BTreeMap;
use shared::{EchoesError, Result};

/// Meteora DLMM LB Pair 主要状态
/// 表示一个完整的DLMM流动性池
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MeteoraLbPairState {
    /// 池地址
    pub address: Pubkey,
    /// X代币铸币地址
    pub token_x_mint: Pubkey,
    /// Y代币铸币地址
    pub token_y_mint: Pubkey,
    /// X代币小数位数
    pub token_x_decimals: u8,
    /// Y代币小数位数
    pub token_y_decimals: u8,
    /// 当前活跃的bin ID
    pub active_id: i32,
    /// bin步长
    pub bin_step: u16,
    /// 基础费率参数
    pub base_factor: u16,
    /// 基础费用幂因子
    pub base_fee_power_factor: u8,
    /// 可变费用控制参数
    pub variable_fee_control: u32,
    /// 最大费率（基点）
    pub max_fee_rate: u16,
    /// 协议费率分成（基点）
    pub protocol_share: u16,
    /// 过滤周期（用于波动性计算）
    pub filter_period: u16,
    /// 衰减周期
    pub decay_period: u16,
    /// 减少因子
    pub reduction_factor: u16,
    /// 最大波动性累加器
    pub max_volatility_accumulator: u32,
    /// 当前波动性累加器
    pub volatility_accumulator: u32,
    /// 波动性参考值
    pub volatility_reference: u32,
    /// 索引参考值
    pub index_reference: i32,
    /// 最后更新时间戳
    pub last_update_timestamp: i64,
    /// 总费用X
    pub total_fee_x: u64,
    /// 总费用Y
    pub total_fee_y: u64,
    /// 协议费用X
    pub protocol_fee_x: u64,
    /// 协议费用Y
    pub protocol_fee_y: u64,
    /// 累计费用X（每流动性）
    pub fee_x_per_token_complete: u128,
    /// 累计费用Y（每流动性）
    pub fee_y_per_token_complete: u128,
    /// 累计费用X（待处理）
    pub fee_x_pending: u64,
    /// 累计费用Y（待处理）
    pub fee_y_pending: u64,
    /// 奖励信息
    pub reward_infos: Vec<MeteoraRewardInfo>,
    /// Oracle信息
    pub oracle: MeteoraOracle,
    /// Bin数组映射 (array_index -> BinArray)
    pub bin_arrays: BTreeMap<i64, MeteoraLbBinArray>,
    /// 位图扩展
    pub bitmap_extension: Option<MeteoraLbBinArrayBitmapExtension>,
    /// 最后更新时间戳
    pub last_updated_slot: u64,
}

impl MeteoraLbPairState {
    /// 创建新的LB Pair状态
    pub fn new(
        address: Pubkey,
        token_x_mint: Pubkey,
        token_y_mint: Pubkey,
        token_x_decimals: u8,
        token_y_decimals: u8,
        active_id: i32,
        bin_step: u16,
    ) -> Self {
        Self {
            address,
            token_x_mint,
            token_y_mint,
            token_x_decimals,
            token_y_decimals,
            active_id,
            bin_step,
            base_factor: 100, // 默认基础因子
            base_fee_power_factor: 0, // 默认幂因子
            variable_fee_control: 120000, // 默认无动态费用
            max_fee_rate: 1000, // 默认最大 10%
            protocol_share: 1000, // 默认协议分成 10%
            filter_period: 30, // 默认 30 秒过滤周期
            decay_period: 600, // 默认 10 分钟衰减周期
            reduction_factor: 5000, // 默认 50% 减少因子
            max_volatility_accumulator: 350000, // 默认最大波动性
            volatility_accumulator: 6921,
            volatility_reference: 6921,
            index_reference: -4354,
            last_update_timestamp: 1754643303,
            total_fee_x: 0,
            total_fee_y: 0,
            protocol_fee_x: 0,
            protocol_fee_y: 0,
            fee_x_per_token_complete: 0,
            fee_y_per_token_complete: 0,
            fee_x_pending: 0,
            fee_y_pending: 0,
            reward_infos: Vec::new(),
            oracle: MeteoraOracle {
                sample_lifetime: 0,
                size: 0,
                weight: 0,
                index: 0,
                last_update_time: 0,
            },
            bin_arrays: BTreeMap::new(),
            bitmap_extension: None,
            last_updated_slot: 0,
        }
    }

    /// 获取当前活跃bin的价格
    pub fn get_active_bin_price(&self) -> Result<u128> {
        // 计算bin ID对应的价格
        // price = (1 + bin_step / 10000)^bin_id
        let bin_step_decimal = self.bin_step as f64 / 10000.0;
        let base = 1.0 + bin_step_decimal;
        let price_float = base.powf(self.active_id as f64);

        // 转换为Q64.64格式
        let price_q64 = utils::float_to_q64(price_float);
        Ok(price_q64)
    }

    /// 获取指定bin ID的价格
    pub fn get_bin_price(&self, bin_id: i32) -> Result<u128> {
        let bin_step_decimal = self.bin_step as f64 / 10000.0;
        let base = 1.0 + bin_step_decimal;
        let price_float = base.powf(bin_id as f64);

        // 转换为Q64.64格式
        let price_q64 = utils::float_to_q64(price_float);
        Ok(price_q64)
    }

    /// 添加或更新bin数组
    pub fn update_bin_array(&mut self, array_index: i64, bin_array: MeteoraLbBinArray) {
        self.bin_arrays.insert(array_index, bin_array);
    }

    /// 获取指定数组索引的bin数组
    pub fn get_bin_array(&self, array_index: i64) -> Option<&MeteoraLbBinArray> {
        self.bin_arrays.get(&array_index)
    }

    /// 获取所有活跃的bin数组
    pub fn get_active_bin_arrays(&self) -> Vec<(i64, &MeteoraLbBinArray)> {
        if let Some(bitmap) = &self.bitmap_extension {
            let active_indices = bitmap.get_active_bin_array_indices();
            active_indices
                .iter()
                .filter_map(|&index| {
                    self.bin_arrays.get(&index).map(|array| (index, array))
                })
                .collect()
        } else {
            // 如果没有位图，返回所有非空的bin数组
            self.bin_arrays
                .iter()
                .filter(|(_, array)| array.get_total_liquidity() > 0)
                .map(|(&index, array)| (index, array))
                .collect()
        }
    }

    /// 计算总流动性
    pub fn get_total_liquidity(&self) -> u128 {
        self.bin_arrays
            .values()
            .map(|array| array.get_total_liquidity())
            .sum()
    }

    /// 获取流动性分布
    pub fn get_liquidity_distribution(&self) -> Vec<(i32, u128, u128)> {
        let mut distribution = Vec::new();

        for (&array_index, bin_array) in &self.bin_arrays {
            for (bin_index, bin) in bin_array.bins.iter().enumerate() {
                if !bin.is_empty() {
                    // 安全地计算实际的bin ID
                    if let Ok(bin_id) = self.calculate_safe_bin_id(array_index, bin_index) {
                        distribution.push((bin_id, bin.price, bin.liquidity_supply));
                    }
                }
            }
        }

        distribution.sort_by_key(|(bin_id, _, _)| *bin_id);
        distribution
    }

    /// 安全地计算 bin ID，避免溢出
    fn calculate_safe_bin_id(&self, array_index: i64, bin_index: usize) -> Result<i32> {
        use super::types::math_constants::{MAX_BIN_ID, MIN_BIN_ID, MAX_BIN_PER_ARRAY};

        // 检查 bin_index 是否在有效范围内
        if bin_index >= MAX_BIN_PER_ARRAY {
            return Err(EchoesError::InvalidInput(
                format!("bin_index {} exceeds maximum {}", bin_index, MAX_BIN_PER_ARRAY)
            ));
        }

        // 计算 bin_id，检查溢出
        let bin_id_i64 = array_index
            .checked_mul(MAX_BIN_PER_ARRAY as i64)
            .and_then(|val| val.checked_add(bin_index as i64))
            .ok_or_else(|| EchoesError::InvalidState("bin ID calculation overflow".to_string()))?;

        // 检查 bin_id 是否在有效范围内
        if bin_id_i64 < MIN_BIN_ID as i64 || bin_id_i64 > MAX_BIN_ID as i64 {
            return Err(EchoesError::InvalidState(
                format!("bin_id {} is out of range [{}, {}]", bin_id_i64, MIN_BIN_ID, MAX_BIN_ID)
            ));
        }

        Ok(bin_id_i64 as i32)
    }

    /// 计算基础费用
    pub fn get_base_fee(&self) -> Result<u128> {
        let base_fee = (self.base_factor as u128)
            .checked_mul(self.bin_step as u128)
            .ok_or_else(|| EchoesError::InvalidState("Base fee calculation overflow".to_string()))?
            .checked_mul(10u128)
            .ok_or_else(|| EchoesError::InvalidState("Base fee calculation overflow".to_string()))?
            .checked_mul(10u128.pow(self.base_fee_power_factor as u32))
            .ok_or_else(|| EchoesError::InvalidState("Base fee calculation overflow".to_string()))?;
        Ok(base_fee)
    }

    /// 计算可变费用（基于波动性）
    pub fn get_variable_fee(&self) -> Result<u128> {
        self.compute_variable_fee(self.volatility_accumulator)
    }

    /// 根据波动性累加器计算可变费用
    pub fn compute_variable_fee(&self, volatility_accumulator: u32) -> Result<u128> {
        if self.variable_fee_control > 0 {
            let volatility_accumulator = volatility_accumulator as u128;
            let bin_step = self.bin_step as u128;
            let variable_fee_control = self.variable_fee_control as u128;

            let square_vfa_bin = volatility_accumulator
                .checked_mul(bin_step)
                .ok_or_else(|| EchoesError::InvalidState("Variable fee calculation overflow".to_string()))?
                .checked_pow(2)
                .ok_or_else(|| EchoesError::InvalidState("Variable fee calculation overflow".to_string()))?;

            let v_fee = variable_fee_control
                .checked_mul(square_vfa_bin)
                .ok_or_else(|| EchoesError::InvalidState("Variable fee calculation overflow".to_string()))?;

            let scaled_v_fee = v_fee
                .checked_add(99_999_999_999)
                .ok_or_else(|| EchoesError::InvalidState("Variable fee scaling overflow".to_string()))?
                .checked_div(100_000_000_000)
                .ok_or_else(|| EchoesError::InvalidState("Variable fee scaling underflow".to_string()))?;

            return Ok(scaled_v_fee);
        }
        Ok(0)
    }

    /// 计算总费率
    pub fn get_total_fee_rate(&self) -> Result<u128> {
        let total_fee_rate = self
            .get_base_fee()?
            .checked_add(self.get_variable_fee()?)
            .ok_or_else(|| EchoesError::InvalidState("Total fee rate overflow".to_string()))?;
        let max_fee_rate = self.max_fee_rate as u128 * math_constants::BASIS_POINT_MAX as u128 / 10000;
        Ok(std::cmp::min(total_fee_rate, max_fee_rate))
    }

    /// 计算交易手续费（从输入金额中扣除）
    pub fn compute_fee(&self, amount: u64) -> Result<u64> {
        let total_fee_rate = self.get_total_fee_rate()?;
        let fee_precision = 1_000_000_000_000u128; // FEE_PRECISION

        let denominator = fee_precision
            .checked_sub(total_fee_rate)
            .ok_or_else(|| EchoesError::InvalidState("Fee denominator underflow".to_string()))?;

        // Ceil division
        let fee = (amount as u128)
            .checked_mul(total_fee_rate)
            .ok_or_else(|| EchoesError::InvalidState("Fee calculation overflow".to_string()))?
            .checked_add(denominator)
            .ok_or_else(|| EchoesError::InvalidState("Fee calculation overflow".to_string()))?
            .checked_sub(1)
            .ok_or_else(|| EchoesError::InvalidState("Fee calculation underflow".to_string()))?
            .checked_div(denominator)
            .ok_or_else(|| EchoesError::InvalidState("Fee division error".to_string()))?;

        Ok(fee as u64)
    }

    /// 计算协议手续费
    pub fn compute_protocol_fee(&self, fee_amount: u64) -> Result<u64> {
        let protocol_fee = (fee_amount as u128)
            .checked_mul(self.protocol_share as u128)
            .ok_or_else(|| EchoesError::InvalidState("Protocol fee calculation overflow".to_string()))?
            .checked_div(math_constants::BASIS_POINT_MAX as u128)
            .ok_or_else(|| EchoesError::InvalidState("Protocol fee division error".to_string()))?;

        Ok(protocol_fee as u64)
    }

    /// 从包含费用的金额中计算费用
    pub fn compute_fee_from_amount(&self, amount_with_fees: u64) -> Result<u64> {
        let total_fee_rate = self.get_total_fee_rate()?;
        let fee_precision = 1_000_000_000_000u128; // FEE_PRECISION

        let fee_amount = (amount_with_fees as u128)
            .checked_mul(total_fee_rate)
            .ok_or_else(|| EchoesError::InvalidState("Fee from amount calculation overflow".to_string()))?
            .checked_add(fee_precision - 1)
            .ok_or_else(|| EchoesError::InvalidState("Fee from amount calculation overflow".to_string()))?
            .checked_div(fee_precision)
            .ok_or_else(|| EchoesError::InvalidState("Fee from amount division error".to_string()))?;

        Ok(fee_amount as u64)
    }

    /// 更新参考值（用于波动性计算）
    pub fn update_references(&mut self, current_timestamp: i64) -> Result<()> {
        let elapsed = current_timestamp
            .checked_sub(self.last_update_timestamp)
            .ok_or_else(|| EchoesError::InvalidState("Timestamp overflow".to_string()))?;

        // 不是高频交易
        if elapsed >= self.filter_period as i64 {
            // 更新最后交易的活跃 ID
            self.index_reference = self.active_id;
            // filter_period < t < decay_period. 衰减时间窗口
            if elapsed < self.decay_period as i64 {
                let volatility_reference = (self.volatility_accumulator as u32)
                    .checked_mul(self.reduction_factor as u32)
                    .ok_or_else(|| EchoesError::InvalidState("Volatility reference overflow".to_string()))?
                    .checked_div(math_constants::BASIS_POINT_MAX as u32)
                    .ok_or_else(|| EchoesError::InvalidState("Volatility reference division error".to_string()))?;

                self.volatility_reference = volatility_reference;
            }
            // 超出衰减时间窗口
            else {
                self.volatility_reference = 0;
            }
        }

        Ok(())
    }

    /// 更新波动性累加器
    pub fn update_volatility_accumulator(&mut self) -> Result<()> {
        let delta_id = (self.index_reference as i64)
            .checked_sub(self.active_id as i64)
            .ok_or_else(|| EchoesError::InvalidState("Delta ID overflow".to_string()))?
            .unsigned_abs();

        let volatility_accumulator = (self.volatility_reference as u64)
            .checked_add(
                delta_id
                    .checked_mul(math_constants::BASIS_POINT_MAX as u64)
                    .ok_or_else(|| EchoesError::InvalidState("Volatility accumulator overflow".to_string()))?
            )
            .ok_or_else(|| EchoesError::InvalidState("Volatility accumulator overflow".to_string()))?;

        self.volatility_accumulator = std::cmp::min(
            volatility_accumulator,
            self.max_volatility_accumulator as u64,
        ) as u32;

        Ok(())
    }
}




