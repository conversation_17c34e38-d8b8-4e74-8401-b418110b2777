//! Raydium CLMM 类型定义
//!
//! 定义 Raydium CLMM 特有的数据结构和类型

use serde::{Deserialize, Serialize};
use solana_sdk::pubkey::Pubkey;
use std::str::FromStr;
use shared::{EchoesError, Result};

/// Q64.64 定点数常量
pub mod math_constants {
    /// Q64.64 格式的 1.0
    pub const Q64_64_ONE: u128 = 1u128 << 64;
    /// 最大 sqrt_price (对应 tick = 443636)
    pub const MAX_SQRT_PRICE: u128 = 79226673515401279992447579055;
    /// 最小 sqrt_price (对应 tick = -443636)
    pub const MIN_SQRT_PRICE: u128 = 4295128739;
    /// 最大 tick
    pub const MAX_TICK: i32 = 443636;
    /// 最小 tick
    pub const MIN_TICK: i32 = -443636;
    /// tick 基数 1.0001
    pub const TICK_BASE: f64 = 1.0001;
}

/// 精确的 Q64.64 定点数学运算
pub mod fixed_point_math {
    use super::math_constants::*;
    use shared::{EchoesError, Result};

    /// 将 tick 转换为 sqrt_price (Q64.64 格式)
    /// 使用精确的数学计算: sqrt_price = 1.0001^(tick/2) * 2^64
    pub fn tick_to_sqrt_price(tick: i32) -> Result<u128> {
        if tick < MIN_TICK || tick > MAX_TICK {
            return Err(EchoesError::InvalidInput(format!("Tick {} out of bounds", tick)));
        }

        // 特殊情况：tick = 0 对应 sqrt_price = 1.0
        if tick == 0 {
            return Ok(Q64_64_ONE);
        }

        // 使用高精度浮点数计算，然后转换为定点数
        // sqrt_price = 1.0001^(tick/2)
        let tick_half = tick as f64 / 2.0;
        let sqrt_price_float = TICK_BASE.powf(tick_half);

        // 转换为 Q64.64 格式
        let sqrt_price_q64 = (sqrt_price_float * (1u128 << 64) as f64) as u128;

        // 确保结果在有效范围内
        let result = sqrt_price_q64.max(MIN_SQRT_PRICE).min(MAX_SQRT_PRICE);
        Ok(result)
    }

    /// 将 sqrt_price (Q64.64) 转换为 tick
    pub fn sqrt_price_to_tick(sqrt_price: u128) -> Result<i32> {
        if sqrt_price < MIN_SQRT_PRICE || sqrt_price > MAX_SQRT_PRICE {
            return Err(EchoesError::InvalidInput(format!("sqrt_price {} out of bounds", sqrt_price)));
        }

        // 使用二分查找来找到对应的 tick
        let mut low = MIN_TICK;
        let mut high = MAX_TICK;

        while low <= high {
            let mid = (low + high) / 2;
            let mid_sqrt_price = tick_to_sqrt_price(mid)?;

            if mid_sqrt_price == sqrt_price {
                return Ok(mid);
            } else if mid_sqrt_price < sqrt_price {
                low = mid + 1;
            } else {
                high = mid - 1;
            }
        }

        Ok(high)
    }

    /// Q64.64 格式的乘法
    pub fn mul_q64_64(a: u128, b: u128) -> Result<u128> {
        // 高质量 Q64.64 乘法，避免溢出
        // (a * b) >> 64
        let a_hi = a >> 64;
        let a_lo = a & ((1u128 << 64) - 1);
        let b_hi = b >> 64;
        let b_lo = b & ((1u128 << 64) - 1);

        // 执行部分乘法
        let lo_lo = a_lo * b_lo;
        let lo_hi = a_lo * b_hi;
        let hi_lo = a_hi * b_lo;
        let hi_hi = a_hi * b_hi;

        // 组合结果，右移64位
        let result = (lo_lo >> 64) + lo_hi + hi_lo + (hi_hi << 64);
        Ok(result)
    }

    /// Q64.64 格式的除法
    pub fn div_q64_64(a: u128, b: u128) -> Result<u128> {
        if b == 0 {
            return Err(EchoesError::InvalidInput("Division by zero".to_string()));
        }

        // 使用高精度除法避免溢出
        // result = (a << 64) / b
        let a_hi = a >> 64;
        let a_lo = a & ((1u128 << 64) - 1);

        // 分别计算高位和低位部分
        let result_hi = (a_hi << 64) / b;
        let remainder_hi = (a_hi << 64) % b;
        let result_lo = ((remainder_hi << 64) + (a_lo << 64)) / b;

        let result = result_hi + result_lo;

        Ok(result)
    }
}

/// AMM 数学计算模块
pub mod amm_math {
    use super::fixed_point_math::*;
    use super::math_constants::*;
    use shared::{EchoesError, Result};

    /// 计算给定输入量的输出量 (基于实际输入量的简化计算)
    pub fn get_amount_out(
        amount_in: u128,
        sqrt_price_current: u128,
        sqrt_price_target: u128,
        liquidity: u128,
        zero_for_one: bool,
    ) -> Result<u128> {
        if liquidity == 0 || amount_in == 0 {
            return Ok(0);
        }

        // 简化但正确的价格计算
        // sqrt_price 是 Q64.64 格式，实际价格 = (sqrt_price / 2^64)^2
        let sqrt_price_f64 = sqrt_price_current as f64 / (Q64_64_ONE as f64);
        let price = sqrt_price_f64 * sqrt_price_f64;

        if zero_for_one {
            // Token0 -> Token1: SOL -> USDT
            // 输入：lamports (1 lamport = 10^-9 SOL)
            // 输出：micro USDT (1 micro USDT = 10^-6 USDT)
            // 注意：get_current_price() 已经调整了小数位差异，返回的价格单位是 micro USDT / lamport
            // 因此直接相乘即可：amount_out = amount_in_lamports * price_micro_usdt_per_lamport
            let amount_out_f64 = (amount_in as f64) * price;
            Ok(amount_out_f64 as u128)
        } else {
            // Token1 -> Token0: USDT -> SOL
            // 输入：micro USDT (1 micro USDT = 10^-6 USDT)
            // 输出：lamports (1 lamport = 10^-9 SOL)
            // 注意：get_current_price() 返回的价格单位是 micro USDT / lamport
            // 因此：amount_out = amount_in_micro_usdt / price_micro_usdt_per_lamport
            let amount_out_f64 = (amount_in as f64) / price;
            Ok(amount_out_f64 as u128)
        }
    }

    /// 计算给定输出量需要的输入量
    pub fn get_amount_in(
        amount_out: u128,
        sqrt_price_current: u128,
        _sqrt_price_target: u128,
        _liquidity: u128,
        zero_for_one: bool,
    ) -> Result<u128> {
        if amount_out == 0 {
            return Ok(0);
        }

        // 简化但正确的价格计算 - 与 get_amount_out 保持一致
        // sqrt_price 是 Q64.64 格式，实际价格 = (sqrt_price / 2^64)^2
        let sqrt_price_f64 = sqrt_price_current as f64 / (Q64_64_ONE as f64);
        let price = sqrt_price_f64 * sqrt_price_f64;

        if zero_for_one {
            // Token0 -> Token1: SOL -> USDT
            // 输出：micro USDT，输入：lamports
            // 注意：get_current_price() 返回的价格单位是 micro USDT / lamport
            // 因此：amount_in = amount_out_micro_usdt / price_micro_usdt_per_lamport
            let amount_in_f64 = (amount_out as f64) / price;
            Ok(amount_in_f64 as u128)
        } else {
            // Token1 -> Token0: USDT -> SOL
            // 输出：lamports，输入：micro USDT
            // 注意：get_current_price() 返回的价格单位是 micro USDT / lamport
            // 因此：amount_in = amount_out_lamports * price_micro_usdt_per_lamport
            let amount_in_f64 = (amount_out as f64) * price;
            Ok(amount_in_f64 as u128)
        }
    }
}

/// 位图操作模块 - 用于高效的 tick 查找
pub mod bitmap_utils {

    /// 在位图字中查找下一个初始化的 tick
    /// 
    /// 参数:
    /// - bitmap: 64位位图，每一位代表一个 tick 是否初始化
    /// - current_tick: 当前 tick 位置
    /// - tick_spacing: tick 间距
    /// - left_to_right: true=向右查找(更大的tick), false=向左查找(更小的tick)
    /// 
    /// 返回: Option<(next_tick, is_initialized)>
    pub fn next_initialized_tick_within_one_word(
        bitmap: u64,
        current_tick: i32,
        tick_spacing: i32,
        left_to_right: bool,
    ) -> Option<(i32, bool)> {
        // 空位图直接返回
        if bitmap == 0 {
            return None;
        }

        // 确保 tick_spacing 为正数
        if tick_spacing <= 0 {
            return None;
        }

        // Step 1: 计算当前 tick 对应的压缩 tick 索引
        // 使用正确的向下取整除法 (div_euclid)
        let compressed_tick = current_tick.div_euclid(tick_spacing);
        
        // Step 2: 确定此位图字覆盖的压缩 tick 范围
        // 每个位图字覆盖 64 个连续的压缩 tick
        let word_pos = compressed_tick.div_euclid(64);
        
        // 此位图字的起始压缩 tick 索引
        let word_start_compressed = word_pos * 64;
        
        if left_to_right {
            // 向右查找：寻找更大的 tick
            // 起始搜索位置：当前位置的下一位，确保找到的 tick 严格大于 current_tick
            let search_start = if compressed_tick >= word_start_compressed && 
                                 compressed_tick < word_start_compressed + 64 {
                // current_tick 在此字范围内，从下一个压缩 tick 开始搜索
                let next_compressed = compressed_tick + 1;
                if next_compressed < word_start_compressed + 64 {
                    (next_compressed - word_start_compressed) as u8
                } else {
                    return None; // 已经超出此字范围
                }
            } else {
                0 // current_tick 不在此字范围内，从字的开始搜索
            };
            
            // 在位图中搜索下一个设置的位
            for bit_index in search_start..64 {
                if (bitmap >> bit_index) & 1 != 0 {
                    // 找到初始化的位，计算对应的 tick
                    let found_compressed = word_start_compressed + bit_index as i32;
                    let found_tick = found_compressed * tick_spacing;
                    
                    // 确保找到的 tick 严格大于 current_tick
                    if found_tick > current_tick {
                        return Some((found_tick, true));
                    }
                }
            }
        } else {
            // 向左查找：寻找更小的 tick
            // 起始搜索位置：当前位置的前一位，确保找到的 tick 严格小于 current_tick
            let search_start = if compressed_tick >= word_start_compressed && 
                                 compressed_tick < word_start_compressed + 64 {
                // current_tick 在此字范围内，从前一个压缩 tick 开始搜索
                let prev_compressed = compressed_tick - 1;
                if prev_compressed >= word_start_compressed {
                    (prev_compressed - word_start_compressed) as u8
                } else {
                    return None; // 已经超出此字范围
                }
            } else {
                63 // current_tick 不在此字范围内，从字的末尾搜索
            };
            
            // 在位图中反向搜索下一个设置的位
            for bit_index in (0..=search_start).rev() {
                if (bitmap >> bit_index) & 1 != 0 {
                    // 找到初始化的位，计算对应的 tick
                    let found_compressed = word_start_compressed + bit_index as i32;
                    let found_tick = found_compressed * tick_spacing;
                    
                    // 确保找到的 tick 严格小于 current_tick
                    if found_tick < current_tick {
                        return Some((found_tick, true));
                    }
                }
            }
        }

        None
    }

    /// 计算 tick 对应的位图字索引和位位置
    /// 遵循 Raydium CLMM 官方的位图逻辑
    pub fn position(tick: i32, tick_spacing: i32) -> (i16, u8) {
        // Step 1: 将 tick 转换为压缩索引
        let compressed = tick / tick_spacing;

        // Step 2: 计算tick_array的索引
        // 每个tick_array包含60个tick，所以tick_array_index = tick / (tick_spacing * 60)
        // 对于负数，需要向下取整（更负的方向）
        let tick_array_size = tick_spacing * 60;
        let tick_array_index = if tick >= 0 {
            tick / tick_array_size
        } else {
            // 对于负数，使用向下取整除法
            (tick - tick_array_size + 1) / tick_array_size
        };

        // Step 3: 计算在位图中的字索引和位位置
        // 每个字包含64位，每位代表一个tick_array
        let word_pos = if tick_array_index >= 0 {
            tick_array_index / 64
        } else {
            // 对于负数，使用向下取整除法
            (tick_array_index - 63) / 64
        };

        let bit_pos = if tick_array_index >= 0 {
            (tick_array_index % 64) as u8
        } else {
            // 对于负数，使用 Euclidean 模运算确保结果在 [0, 63] 范围内
            let remainder = tick_array_index % 64;
            if remainder < 0 {
                (remainder + 64) as u8
            } else {
                remainder as u8
            }
        };

        (word_pos as i16, bit_pos)
    }

    /// 专门用于 Raydium 位图搜索的安全位置计算
    /// 确保返回的索引在位图数组范围内
    pub fn safe_position_for_bitmap_array(tick: i32, tick_spacing: i32) -> Option<(usize, u8)> {
        let (word_pos, bit_pos) = position(tick, tick_spacing);

        // 位图数组有16个元素，索引8为中心
        // 有效的 word_pos 范围是 [-8, 7]
        let word_index_i16 = word_pos + 8; // 偏移到数组中心

        if word_index_i16 >= 0 && word_index_i16 < 16 {
            Some((word_index_i16 as usize, bit_pos))
        } else {
            None
        }
    }

    /// 翻转位图中的位
    pub fn flip_tick(bitmap: &mut [u64; 16], tick: i32, tick_spacing: i32) {
        let (word_pos, bit_pos) = position(tick, tick_spacing);

        // 安全地计算数组索引，避免整数下溢
        let word_index_i16 = word_pos + 8; // 偏移到数组中心

        // 确保索引在有效范围内
        if word_index_i16 >= 0 && word_index_i16 < 16 {
            let word_index = word_index_i16 as usize;
            let mask = 1u64 << (bit_pos % 64);
            bitmap[word_index] ^= mask;
        }
    }

    /// 检查 tick 是否已初始化
    pub fn is_initialized(bitmap: &[u64; 16], tick: i32, tick_spacing: i32) -> bool {
        let (word_pos, bit_pos) = position(tick, tick_spacing);

        // 安全地计算数组索引，避免整数下溢
        let word_index_i16 = word_pos + 8; // 偏移到数组中心

        // 确保索引在有效范围内
        if word_index_i16 >= 0 && word_index_i16 < 16 {
            let word_index = word_index_i16 as usize;
            let mask = 1u64 << (bit_pos % 64);
            bitmap[word_index] & mask != 0
        } else {
            false
        }
    }
}

/// 高精度数学计算 trait
pub trait MulDiv {
    type Output;

    /// 计算 floor(self * numerator / denominator)
    fn mul_div_floor(self, numerator: Self, denominator: Self) -> Option<Self::Output>;

    /// 计算 ceil(self * numerator / denominator)
    fn mul_div_ceil(self, numerator: Self, denominator: Self) -> Option<Self::Output>;
}

impl MulDiv for u64 {
    type Output = u64;

    fn mul_div_floor(self, numerator: Self, denominator: Self) -> Option<Self::Output> {
        if denominator == 0 {
            return None;
        }
        // 使用 u128 防止溢出
        let result = (self as u128 * numerator as u128) / denominator as u128;
        if result > u64::MAX as u128 {
            None
        } else {
            Some(result as u64)
        }
    }

    fn mul_div_ceil(self, numerator: Self, denominator: Self) -> Option<Self::Output> {
        if denominator == 0 {
            return None;
        }
        let product = self as u128 * numerator as u128;
        let result = (product + denominator as u128 - 1) / denominator as u128;
        if result > u64::MAX as u128 {
            None
        } else {
            Some(result as u64)
        }
    }
}

impl MulDiv for u128 {
    type Output = u128;

    fn mul_div_floor(self, numerator: Self, denominator: Self) -> Option<Self::Output> {
        if denominator == 0 {
            return None;
        }
        // 对于 u128，需要使用更大的类型或者分解计算来避免溢出
        // 简化实现：检查是否会溢出
        if self > 0 && numerator > u128::MAX / self {
            // 使用浮点数作为近似（在生产环境中应该使用更精确的大数运算）
            let result = (self as f64 * numerator as f64 / denominator as f64).floor();
            if result > u128::MAX as f64 {
                None
            } else {
                Some(result as u128)
            }
        } else {
            Some((self * numerator) / denominator)
        }
    }

    fn mul_div_ceil(self, numerator: Self, denominator: Self) -> Option<Self::Output> {
        if denominator == 0 {
            return None;
        }
        if self > 0 && numerator > u128::MAX / self {
            let result = (self as f64 * numerator as f64 / denominator as f64).ceil();
            if result > u128::MAX as f64 {
                None
            } else {
                Some(result as u128)
            }
        } else {
            let product = self * numerator;
            Some((product + denominator - 1) / denominator)
        }
    }
}

/// 费用计算模块
pub mod fee_math {
    use super::MulDiv;

    /// 费用率分母值（官方 Raydium 使用的常量）
    pub const FEE_RATE_DENOMINATOR_VALUE: u32 = 1_000_000;

    /// 计算交换费用（官方公式）
    pub fn compute_fee_amount(amount: u128, fee_rate: u32) -> u64 {
        // 官方公式：fee = amount * fee_rate / FEE_RATE_DENOMINATOR_VALUE
        amount.mul_div_floor(fee_rate as u128, FEE_RATE_DENOMINATOR_VALUE as u128)
            .unwrap_or(0) as u64
    }

    /// 官方费用计算：从输入量中计算扣除费用后的净输入量
    pub fn amount_remaining_less_fee(amount_remaining: u64, fee_rate: u32) -> u64 {
        // 官方公式：amount_remaining_less_fee = amount_remaining * (FEE_RATE_DENOMINATOR_VALUE - fee_rate) / FEE_RATE_DENOMINATOR_VALUE
        amount_remaining.mul_div_floor(
            (FEE_RATE_DENOMINATOR_VALUE - fee_rate) as u64,
            FEE_RATE_DENOMINATOR_VALUE as u64
        ).unwrap_or(0)
    }

    /// 从输入量中扣除费用后的净输入量（兼容性保持）
    #[deprecated(note = "使用 amount_remaining_less_fee 替代")]
    pub fn amount_after_fee(amount_in: u128, fee_rate: u32) -> u128 {
        let fee = compute_fee_amount(amount_in, fee_rate) as u128;
        amount_in.saturating_sub(fee)
    }
}

/// 流动性数学计算模块
pub mod liquidity_math {
    use shared::{EchoesError, Result};

    /// 添加流动性变化量（精确数学运算，模仿官方 liquidity_math::add_delta）
    pub fn add_delta(liquidity: u128, delta: i128) -> Result<u128> {
        if delta < 0 {
            let delta_abs = (-delta) as u128;
            if liquidity < delta_abs {
                return Err(EchoesError::InvalidInput(
                    "Liquidity subtraction would result in underflow".to_string()
                ));
            }
            Ok(liquidity - delta_abs)
        } else {
            let delta_abs = delta as u128;
            match liquidity.checked_add(delta_abs) {
                Some(result) => Ok(result),
                None => Err(EchoesError::InvalidInput(
                    "Liquidity addition would result in overflow".to_string()
                )),
            }
        }
    }
}

/// 奖励信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RewardInfo {
    /// 奖励状态
    pub reward_state: u8,
    /// 开始时间
    pub open_time: u64,
    /// 结束时间
    pub end_time: u64,
    /// 最后更新时间
    pub last_update_time: u64,
    /// 每秒发放量 (Q64.64格式)
    pub emissions_per_second_x64: u128,
    /// 总发放量
    pub reward_total_emissioned: u64,
    /// 已领取量
    pub reward_claimed: u64,
    /// 奖励代币铸币地址
    pub token_mint: Pubkey,
    /// 奖励代币库地址
    pub token_vault: Pubkey,
    /// 权限地址
    pub authority: Pubkey,
    /// 全局奖励增长 (Q64.64格式)
    pub reward_growth_global_x64: u128,
}

/// Tick信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TickInfo {
    /// Tick值
    pub tick: i32,
    /// 净流动性变化
    pub liquidity_net: i128,
    /// 总流动性
    pub liquidity_gross: u128,
    /// 费用增长外部 Token 0 (Q64.64格式)
    pub fee_growth_outside_0_x64: u128,
    /// 费用增长外部 Token 1 (Q64.64格式)
    pub fee_growth_outside_1_x64: u128,
    /// 奖励增长外部 (Q64.64格式) - 支持3个奖励
    pub reward_growths_outside_x64: [u128; 3],
}

/// Tick数组状态
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TickArrayState {
    /// 所属池ID
    pub pool_id: Pubkey,
    /// 起始tick索引
    pub start_tick_index: i32,
    /// Tick信息数组 (88个tick)
    pub ticks: Vec<TickInfo>,
    /// 已初始化的tick数量
    pub initialized_tick_count: u16,
    /// 最近周期
    pub recent_epoch: u64,
}

impl TickArrayState {
    /// 从JSON数据创建TickArrayState
    pub fn from_json_value(value: &serde_json::Value) -> Result<Self> {
        let data = value["parsed"]["data"].as_object()
            .ok_or_else(|| EchoesError::Parsing("Invalid tick array JSON structure".to_string()))?;

        let pool_id = Pubkey::from_str(
            data["poolId"].as_str()
                .ok_or_else(|| EchoesError::Parsing("Missing poolId".to_string()))?
        ).map_err(|e| EchoesError::Parsing(format!("Invalid poolId: {}", e)))?;

        let start_tick_index = data["startTickIndex"].as_i64()
            .ok_or_else(|| EchoesError::Parsing("Missing startTickIndex".to_string()))? as i32;

        let ticks_array = data["ticks"].as_array()
            .ok_or_else(|| EchoesError::Parsing("Missing ticks array".to_string()))?;

        let mut ticks = Vec::new();
        let mut initialized_count = 0u16;

        for tick_value in ticks_array {
            let tick_obj = tick_value.as_object()
                .ok_or_else(|| EchoesError::Parsing("Invalid tick structure".to_string()))?;

            let liquidity_gross_str = tick_obj["liquidityGross"].as_str()
                .ok_or_else(|| EchoesError::Parsing("Missing liquidityGross".to_string()))?;
            let liquidity_gross = liquidity_gross_str.parse::<u128>()
                .map_err(|e| EchoesError::Parsing(format!("Invalid liquidityGross: {}", e)))?;

            // 只有 liquidityGross > 0 的 tick 才被认为是已初始化的
            if liquidity_gross > 0 {
                initialized_count += 1;
            }

            let tick_info = TickInfo {
                tick: tick_obj["tick"].as_i64()
                    .ok_or_else(|| EchoesError::Parsing("Missing tick".to_string()))? as i32,

                liquidity_net: tick_obj["liquidityNet"].as_str()
                    .ok_or_else(|| EchoesError::Parsing("Missing liquidityNet".to_string()))?
                    .parse::<i128>()
                    .map_err(|e| EchoesError::Parsing(format!("Invalid liquidityNet: {}", e)))?,

                liquidity_gross,

                fee_growth_outside_0_x64: tick_obj["feeGrowthOutside0X64"].as_str()
                    .ok_or_else(|| EchoesError::Parsing("Missing feeGrowthOutside0X64".to_string()))?
                    .parse::<u128>()
                    .map_err(|e| EchoesError::Parsing(format!("Invalid feeGrowthOutside0X64: {}", e)))?,

                fee_growth_outside_1_x64: tick_obj["feeGrowthOutside1X64"].as_str()
                    .ok_or_else(|| EchoesError::Parsing("Missing feeGrowthOutside1X64".to_string()))?
                    .parse::<u128>()
                    .map_err(|e| EchoesError::Parsing(format!("Invalid feeGrowthOutside1X64: {}", e)))?,

                reward_growths_outside_x64: Self::parse_reward_growths_outside(tick_obj)?,
            };

            ticks.push(tick_info);
        }

        Ok(Self {
            pool_id,
            start_tick_index,
            ticks,
            initialized_tick_count: initialized_count,
            recent_epoch: 0, // 默认值，如果JSON中没有提供
        })
    }

    /// 解析奖励增长外部数组
    fn parse_reward_growths_outside(tick_obj: &serde_json::Map<String, serde_json::Value>) -> Result<[u128; 3]> {
        let rewards_array = tick_obj["rewardGrowthsOutsideX64"].as_array()
            .ok_or_else(|| EchoesError::Parsing("Missing rewardGrowthsOutsideX64".to_string()))?;

        if rewards_array.len() != 3 {
            return Err(EchoesError::Parsing("Invalid rewardGrowthsOutsideX64 length".to_string()));
        }

        let mut rewards = [0u128; 3];
        for (i, reward_value) in rewards_array.iter().enumerate() {
            rewards[i] = reward_value.as_str()
                .ok_or_else(|| EchoesError::Parsing(format!("Invalid reward growth value at index {}", i)))?
                .parse::<u128>()
                .map_err(|e| EchoesError::Parsing(format!("Invalid reward growth value: {}", e)))?;
        }

        Ok(rewards)
    }
}

/// Tick数组位图扩展
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TickArrayBitmapExtension {
    /// 所属池ID
    pub pool_id: Pubkey,
    /// 正向tick数组位图 [14][8]
    pub positive_tick_array_bitmap: [[u64; 8]; 14],
    /// 负向tick数组位图 [14][8]
    pub negative_tick_array_bitmap: [[u64; 8]; 14],
}

impl TickArrayBitmapExtension {
    /// 从JSON数据创建TickArrayBitmapExtension
    pub fn from_json_value(value: &serde_json::Value) -> Result<Self> {
        let data = value["parsed"]["data"].as_object()
            .ok_or_else(|| EchoesError::Parsing("Invalid bitmap extension JSON structure".to_string()))?;

        let pool_id = Pubkey::from_str(
            data["poolId"].as_str()
                .ok_or_else(|| EchoesError::Parsing("Missing poolId".to_string()))?
        ).map_err(|e| EchoesError::Parsing(format!("Invalid poolId: {}", e)))?;

        let positive_bitmap = Self::parse_bitmap_array(data, "positiveTickArrayBitmap")?;
        let negative_bitmap = Self::parse_bitmap_array(data, "negativeTickArrayBitmap")?;

        Ok(Self {
            pool_id,
            positive_tick_array_bitmap: positive_bitmap,
            negative_tick_array_bitmap: negative_bitmap,
        })
    }

    /// 解析位图数组 [14][8]
    fn parse_bitmap_array(
        data: &serde_json::Map<String, serde_json::Value>,
        field_name: &str
    ) -> Result<[[u64; 8]; 14]> {
        let bitmap_array = data[field_name].as_array()
            .ok_or_else(|| EchoesError::Parsing(format!("Missing {}", field_name)))?;

        if bitmap_array.len() != 14 {
            return Err(EchoesError::Parsing(format!("Invalid {} length, expected 14, got {}", field_name, bitmap_array.len())));
        }

        let mut result = [[0u64; 8]; 14];
        for (i, row_value) in bitmap_array.iter().enumerate() {
            let row_array = row_value.as_array()
                .ok_or_else(|| EchoesError::Parsing(format!("Invalid {} row structure at index {}", field_name, i)))?;

            if row_array.len() != 8 {
                return Err(EchoesError::Parsing(format!("Invalid {} row length at index {}, expected 8, got {}", field_name, i, row_array.len())));
            }

            for (j, value) in row_array.iter().enumerate() {
                result[i][j] = value.as_str()
                    .ok_or_else(|| EchoesError::Parsing(format!("Invalid {} value at [{}, {}]", field_name, i, j)))?
                    .parse::<u64>()
                    .map_err(|e| EchoesError::Parsing(format!("Invalid {} value at [{}, {}]: {}", field_name, i, j, e)))?;
            }
        }

        Ok(result)
    }
}

/// 交换配置
#[derive(Debug, Clone)]
pub struct SwapConfig {
    /// 费用率 (basis points, 例如 3000 = 0.3%)
    pub fee_rate: u32,
    /// 协议费用率 (basis points)
    pub protocol_fee_rate: u32,
    /// 最大滑点保护 (basis points)
    pub max_slippage_bps: u32,
    /// 是否启用精确输入模式
    pub exact_in: bool,
}

impl Default for SwapConfig {
    fn default() -> Self {
        Self {
            fee_rate: 3000, // 0.3%
            protocol_fee_rate: 0,
            max_slippage_bps: 100, // 1%
            exact_in: true,
        }
    }
}

/// 增强的交换结果
#[derive(Debug, Clone)]
pub struct SwapResult {
    pub amount_in: u64,
    pub amount_out: u64,
    pub fee_amount: u64,
    pub protocol_fee_amount: u64,
    pub sqrt_price_after: u128,
    pub tick_after: i32,
    pub liquidity_after: u128,
    pub price_impact_bps: u32,
}

/// 交换步骤计算结果
#[derive(Debug, Clone)]
pub struct SwapStepComputation {
    pub sqrt_price_next: u128,
    pub amount_in: u128,
    pub amount_out: u128,
    pub fee_amount: u64,
}

/// 池统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PoolStats {
    pub total_liquidity: u128,
    pub current_tick: i32,
    pub current_price: f64,
    pub total_ticks: usize,
    pub initialized_ticks: usize,
    pub tick_arrays_count: usize,
    pub tvl_token_0: f64,
    pub tvl_token_1: f64,
}

/// 高级交换计算模块
pub mod swap_math {
    use super::*;
    use super::amm_math::*;
    use super::fee_math::*;
    use super::fixed_point_math::*;
    use super::MulDiv;

    /// 计算下一个 sqrt_price (根据输入量)
    pub fn get_next_sqrt_price_from_input(
        sqrt_price: u128,
        liquidity: u128,
        amount_in: u128,
        zero_for_one: bool,
    ) -> Result<u128> {
        if liquidity == 0 {
            return Ok(sqrt_price);
        }

        if zero_for_one {
            // Token0 -> Token1: sqrt_price 下降
            // √P_new = L * √P / (L + Δx * √P)
            let numerator = mul_q64_64(liquidity, sqrt_price)?;
            let delta_product = mul_q64_64(amount_in, sqrt_price)?;
            let denominator = liquidity + delta_product;
            div_q64_64(numerator, denominator)
        } else {
            // Token1 -> Token0: sqrt_price 上升
            // √P_new = √P + Δy / L
            let price_delta = div_q64_64(amount_in, liquidity)?;
            Ok(sqrt_price + price_delta)
        }
    }

    /// 计算下一个 sqrt_price (从输出量)
    pub fn get_next_sqrt_price_from_output(
        sqrt_price: u128,
        liquidity: u128,
        amount_out: u128,
        zero_for_one: bool,
    ) -> Result<u128> {
        if liquidity == 0 {
            return Ok(sqrt_price);
        }

        if zero_for_one {
            // Token0 -> Token1: sqrt_price 下降
            // √P_new = L * √P / (L + Δy * √P)
            let numerator = mul_q64_64(liquidity, sqrt_price)?;
            let denominator_delta = mul_q64_64(amount_out, sqrt_price)?;
            let denominator = liquidity + denominator_delta;
            div_q64_64(numerator, denominator)
        } else {
            // Token1 -> Token0: sqrt_price 上升
            // √P_new = √P + Δx / L
            let price_delta = div_q64_64(amount_out, liquidity)?;
            Ok(sqrt_price + price_delta)
        }
    }

    /// 计算从当前价格到目标价格所需的精确输入量
    pub fn get_amount_in_to_target_price(
        sqrt_price_current: u128,
        sqrt_price_target: u128,
        liquidity: u128,
        zero_for_one: bool,
        exact_in: bool,
    ) -> Result<u128> {
        if liquidity == 0 || sqrt_price_current == sqrt_price_target {
            return Ok(0);
        }

        if exact_in {
            // 精确输入模式：计算达到目标价格需要的输入量
            if zero_for_one {
                // Token0 -> Token1: sqrt_price 下降
                // 公式：Δx = L * (1/√P_target - 1/√P_current)
                // 重排为：Δx = L * (√P_current - √P_target) / (√P_target * √P_current)
                if sqrt_price_target >= sqrt_price_current {
                    return Ok(0); // 价格不会下降，无需输入
                }

                let price_diff = sqrt_price_current - sqrt_price_target;
                let numerator = mul_q64_64(liquidity, price_diff)?;
                let denominator = mul_q64_64(sqrt_price_target, sqrt_price_current)?;
                div_q64_64(numerator, denominator)
            } else {
                // Token1 -> Token0: sqrt_price 上升
                // 公式：Δy = L * (√P_target - √P_current)
                if sqrt_price_target <= sqrt_price_current {
                    return Ok(0); // 价格不会上升，无需输入
                }

                let price_diff = sqrt_price_target - sqrt_price_current;
                mul_q64_64(liquidity, price_diff)
            }
        } else {
            // 精确输出模式：计算达到目标价格能产生的输出量
            if zero_for_one {
                // Token0 -> Token1: sqrt_price 下降，输出 Token1
                // 公式：Δy = L * (√P_current - √P_target)
                if sqrt_price_target >= sqrt_price_current {
                    return Ok(0);
                }

                let price_diff = sqrt_price_current - sqrt_price_target;
                mul_q64_64(liquidity, price_diff)
            } else {
                // Token1 -> Token0: sqrt_price 上升，输出 Token0
                // 公式：Δx = L * (1/√P_target - 1/√P_current)
                if sqrt_price_target <= sqrt_price_current {
                    return Ok(0);
                }

                let price_diff = sqrt_price_target - sqrt_price_current;
                let numerator = mul_q64_64(liquidity, price_diff)?;
                let denominator = mul_q64_64(sqrt_price_target, sqrt_price_current)?;
                div_q64_64(numerator, denominator)
            }
        }
    }

    /// 计算单步交换 (修复版 - 正确处理流动性区间边界)
    pub fn compute_swap_step(
        sqrt_price_current: u128,
        sqrt_price_target: u128,
        liquidity: u128,
        amount_remaining: u128,
        fee_rate: u32,
        zero_for_one: bool,
        exact_in: bool,
    ) -> Result<SwapStepComputation> {
        if liquidity == 0 {
            return Ok(SwapStepComputation {
                sqrt_price_next: sqrt_price_current,
                amount_in: 0,
                amount_out: 0,
                fee_amount: 0,
            });
        }

        println!("compute_swap_step: current={}, target={}, liquidity={}, remaining={}, fee_rate={}, zero_for_one={}, exact_in={}",
            sqrt_price_current, sqrt_price_target, liquidity, amount_remaining, fee_rate, zero_for_one, exact_in);

        // 步骤1: 计算到达目标价格所需的精确输入量
        let amount_needed_to_target = get_amount_in_to_target_price(
            sqrt_price_current,
            sqrt_price_target,
            liquidity,
            zero_for_one,
            exact_in,
        )?;

        println!("amount_needed_to_target: {}", amount_needed_to_target);

        // 步骤2: 计算实际使用的输入量（取较小值）
        let (actual_amount_in, reached_target) = if exact_in {
            // 精确输入模式：考虑费用后的输入量
            let amount_remaining_after_fee = amount_remaining_less_fee(amount_remaining as u64, fee_rate) as u128;

            if amount_needed_to_target == 0 || amount_remaining_after_fee >= amount_needed_to_target {
                // 可以达到目标价格
                (amount_needed_to_target, true)
            } else {
                // 无法达到目标价格，使用所有剩余量
                (amount_remaining_after_fee, false)
            }
        } else {
            // 精确输出模式
            if amount_needed_to_target == 0 || amount_remaining >= amount_needed_to_target {
                // 可以达到目标价格
                (amount_needed_to_target, true)
            } else {
                // 无法达到目标价格，使用所有剩余量
                (amount_remaining, false)
            }
        };

        println!("actual_amount_in: {}, reached_target: {}", actual_amount_in, reached_target);

        // 步骤3: 计算新的价格
        let sqrt_price_next = if reached_target {
            sqrt_price_target
        } else {
            // 根据实际输入量计算新价格
            if exact_in {
                get_next_sqrt_price_from_input(
                    sqrt_price_current,
                    liquidity,
                    actual_amount_in,
                    zero_for_one,
                )?
            } else {
                get_next_sqrt_price_from_output(
                    sqrt_price_current,
                    liquidity,
                    actual_amount_in,
                    zero_for_one,
                )?
            }
        };

        println!("sqrt_price_next: {}", sqrt_price_next);

        // 步骤4: 计算输入和输出量
        let (amount_in, amount_out) = if exact_in {
            // 精确输入模式
            let amount_out = get_amount_out(
                actual_amount_in,
                sqrt_price_current,
                sqrt_price_next,
                liquidity,
                zero_for_one,
            )?;

            // 计算包含费用的总输入量
            let total_amount_in = if actual_amount_in == 0 {
                0
            } else {
                // 反推包含费用的输入量：amount_with_fee = amount_after_fee * FEE_RATE_DENOMINATOR_VALUE / (FEE_RATE_DENOMINATOR_VALUE - fee_rate)
                ((actual_amount_in as u64).mul_div_ceil(
                    FEE_RATE_DENOMINATOR_VALUE as u64,
                    (FEE_RATE_DENOMINATOR_VALUE - fee_rate) as u64
                ).unwrap_or(0) as u128).min(amount_remaining)
            };

            (total_amount_in, amount_out)
        } else {
            // 精确输出模式
            let amount_in = get_amount_in(
                actual_amount_in,
                sqrt_price_current,
                sqrt_price_next,
                liquidity,
                zero_for_one,
            )?;
            (amount_in, actual_amount_in)
        };

        // 步骤5: 计算费用
        let fee_amount = if exact_in {
            // 精确输入模式：费用 = 总输入量 - 实际用于交换的量
            (amount_in as u64).saturating_sub(actual_amount_in as u64)
        } else {
            // 精确输出模式：根据输入量计算费用
            (amount_in as u64).mul_div_ceil(
                fee_rate as u64,
                (FEE_RATE_DENOMINATOR_VALUE - fee_rate) as u64
            ).unwrap_or(0)
        };

        println!("Final result: amount_in={}, amount_out={}, fee_amount={}, sqrt_price_next={}",
            amount_in, amount_out, fee_amount, sqrt_price_next);

        Ok(SwapStepComputation {
            sqrt_price_next,
            amount_in,
            amount_out,
            fee_amount,
        })
    }
}

/// Raydium CLMM 位置信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RaydiumClmmPositionInfo {
    /// 位置 NFT 铸币地址
    pub nft_mint: Pubkey,
    /// 池 ID
    pub pool_id: Pubkey,
    /// 下边界 Tick
    pub tick_lower_index: i32,
    /// 上边界 Tick
    pub tick_upper_index: i32,
    /// 流动性
    pub liquidity: u128,
    /// 费用增长内部 X
    pub fee_growth_inside_last_x64: u128,
    /// 费用增长内部 Y
    pub fee_growth_inside_last_y64: u128,
    /// 代币欠费 X
    pub token_fees_owed_x: u64,
    /// 代币欠费 Y
    pub token_fees_owed_y: u64,
    /// 奖励增长内部
    pub reward_growth_inside_last_x64: [u128; 3],
    /// 奖励欠费
    pub reward_amounts_owed: [u64; 3],
}

/// Raydium CLMM 错误类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum RaydiumClmmError {
    /// 无效的 Tick 范围
    InvalidTickRange,
    /// 无效的流动性
    InvalidLiquidity,
    /// 无效的价格
    InvalidPrice,
    /// Tick 不存在
    TickNotFound,
    /// 位置不存在
    PositionNotFound,
    /// 计算溢出
    CalculationOverflow,
}

impl std::fmt::Display for RaydiumClmmError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            RaydiumClmmError::InvalidTickRange => write!(f, "Invalid tick range"),
            RaydiumClmmError::InvalidLiquidity => write!(f, "Invalid liquidity"),
            RaydiumClmmError::InvalidPrice => write!(f, "Invalid price"),
            RaydiumClmmError::TickNotFound => write!(f, "Tick not found"),
            RaydiumClmmError::PositionNotFound => write!(f, "Position not found"),
            RaydiumClmmError::CalculationOverflow => write!(f, "Calculation overflow"),
        }
    }
}

impl std::error::Error for RaydiumClmmError {}

/// Raydium CLMM 结果类型
pub type RaydiumClmmResult<T> = std::result::Result<T, RaydiumClmmError>;

#[cfg(test)]
mod tests {
    use super::*;
    use super::bitmap_utils::*;

    /// 测试位图位置计算，特别是对负数的处理
    #[test]
    fn test_bitmap_position_calculation() {
        let tick_spacing = 60;

        // 测试正数
        let (word_pos, bit_pos) = position(0, tick_spacing);
        assert_eq!(word_pos, 0);
        assert_eq!(bit_pos, 0);

        let (word_pos, bit_pos) = position(15360, tick_spacing); // 15360 / 60 = 256
        assert_eq!(word_pos, 1); // 256 / 256 = 1
        assert_eq!(bit_pos, 0); // 256 % 256 = 0

        // 测试负数 - 这是关键修复
        let (word_pos, bit_pos) = position(-18241, tick_spacing);
        println!("tick=-18241, word_pos={}, bit_pos={}", word_pos, bit_pos);

        // -18241 / 60 = -304.0167... = -305 (在 Rust 中对负数向零舍入)
        let compressed = -18241 / tick_spacing; // -304
        assert_eq!(compressed, -304);

        // 对于 compressed = -304：
        // word_pos = -304 >> 8 = -2 (算术右移)
        // bit_pos = ((-304 % 256) + 256) % 256 = ((-48) + 256) % 256 = 208
        assert_eq!(word_pos, -2);
        // bit_pos 是 u8 类型，自动确保在 0-255 范围内

        // 测试更多负数情况
        let (word_pos, bit_pos) = position(-60, tick_spacing); // -1 compressed
        assert_eq!(word_pos, -1);
        assert_eq!(bit_pos, 255); // (-1 % 256 + 256) % 256 = 255

        let (word_pos, bit_pos) = position(-15360, tick_spacing); // -256 compressed
        assert_eq!(word_pos, -1); // -256 >> 8 = -1
        assert_eq!(bit_pos, 0); // (-256 % 256 + 256) % 256 = 0
    }

    /// 测试位图数组索引计算（防止整数下溢）
    #[test]
    fn test_bitmap_array_indexing() {
        let tick_spacing = 60;

        // 测试 tick = -18241
        let (word_pos, _) = position(-18241, tick_spacing);
        assert_eq!(word_pos, -2);

        // 计算数组索引（中心偏移 +8）
        let word_index_i16 = word_pos + 8;
        assert_eq!(word_index_i16, 6); // -2 + 8 = 6

        // 确保索引在有效范围内
        assert!(word_index_i16 >= 0 && word_index_i16 < 16);

        let word_index = word_index_i16 as usize;
        assert_eq!(word_index, 6);

        // 测试边界情况
        // 最大负 word_pos = -8，对应 array index = 0
        let word_index_min = (-8i16 + 8) as usize;
        assert_eq!(word_index_min, 0);

        // 最大正 word_pos = 7，对应 array index = 15
        let word_index_max = (7i16 + 8) as usize;
        assert_eq!(word_index_max, 15);
    }

    /// 测试位图初始化检查
    #[test]
    fn test_bitmap_is_initialized() {
        let mut bitmap = [0u64; 16];
        let tick_spacing = 60;

        // 测试负数 tick
        let test_tick = -18241;

        // 初始状态应该为 false
        assert!(!is_initialized(&bitmap, test_tick, tick_spacing));

        // 手动设置位图
        flip_tick(&mut bitmap, test_tick, tick_spacing);

        // 现在应该为 true
        assert!(is_initialized(&bitmap, test_tick, tick_spacing));

        // 再次翻转应该回到 false
        flip_tick(&mut bitmap, test_tick, tick_spacing);
        assert!(!is_initialized(&bitmap, test_tick, tick_spacing));
    }

    /// 测试修复后的 next_initialized_tick_within_one_word 函数
    #[test]
    fn test_next_initialized_tick_within_one_word() {
        let tick_spacing = 60;
        
        // 测试用例1：基本的向右查找
        // 位图: 设置了第1位和第3位 (00001010 in binary)
        let bitmap = (1u64 << 1) | (1u64 << 3);
        let current_tick = 0;
        
        let result = next_initialized_tick_within_one_word(bitmap, current_tick, tick_spacing, true);
        assert_eq!(result, Some((1 * tick_spacing, true))); // 应该找到位1对应的tick
        
        // 测试用例2：基本的向左查找
        let current_tick = 4 * tick_spacing;
        let result = next_initialized_tick_within_one_word(bitmap, current_tick, tick_spacing, false);
        assert_eq!(result, Some((3 * tick_spacing, true))); // 应该找到位3对应的tick
        
        // 测试用例3：负数tick的处理
        let current_tick = -120; // -2 * tick_spacing
        // 使用正确的 div_euclid：-120.div_euclid(60) = -2
        // compressed_tick = -2, word_pos = -1, 在位图字中的位置需要根据实际的word范围计算
        let bitmap_negative = 1u64 << 10; // 设置第10位
        
        // 对于负数tick，我们需要确保位图字覆盖了正确的范围
        // 这里的测试更多是确保函数不会崩溃，具体的tick值需要根据实际的位图字范围调整
        let result = next_initialized_tick_within_one_word(bitmap_negative, current_tick, tick_spacing, true);
        // 应该能找到下一个初始化的tick，或者返回None如果在当前字中没有找到
        
        // 测试用例4：边界条件 - 空位图
        let empty_bitmap = 0u64;
        let result = next_initialized_tick_within_one_word(empty_bitmap, 0, tick_spacing, true);
        assert_eq!(result, None);
        
        // 测试用例5：边界条件 - 无效的tick_spacing
        let result = next_initialized_tick_within_one_word(bitmap, 0, 0, true);
        assert_eq!(result, None);
        
        let result = next_initialized_tick_within_one_word(bitmap, 0, -1, true);
        assert_eq!(result, None);
        
        // 测试用例6：当前tick已经是最后一个初始化的tick
        let bitmap_last = 1u64 << 63; // 只有最后一位被设置
        let current_tick = 63 * tick_spacing;
        let result = next_initialized_tick_within_one_word(bitmap_last, current_tick, tick_spacing, true);
        assert_eq!(result, None); // 向右查找应该找不到更大的tick
        
        // 测试用例7：当前tick是第一个初始化的tick
        let bitmap_first = 1u64; // 只有第0位被设置
        let current_tick = 0;
        let result = next_initialized_tick_within_one_word(bitmap_first, current_tick, tick_spacing, false);
        assert_eq!(result, None); // 向左查找应该找不到更小的tick
    }

    /// 测试压缩tick计算的正确性（使用div_euclid）
    #[test]
    fn test_compressed_tick_calculation() {
        let tick_spacing = 60;
        
        // 正数测试
        assert_eq!(120_i32.div_euclid(tick_spacing), 2);
        assert_eq!(119_i32.div_euclid(tick_spacing), 1);
        assert_eq!(60_i32.div_euclid(tick_spacing), 1);
        assert_eq!(59_i32.div_euclid(tick_spacing), 0);
        assert_eq!(0_i32.div_euclid(tick_spacing), 0);
        
        // 负数测试 - 这里是关键修复
        assert_eq!((-1_i32).div_euclid(tick_spacing), -1);
        assert_eq!((-60_i32).div_euclid(tick_spacing), -1);
        assert_eq!((-61_i32).div_euclid(tick_spacing), -2);
        assert_eq!((-120_i32).div_euclid(tick_spacing), -2);
        assert_eq!((-121_i32).div_euclid(tick_spacing), -3);
        
        // 与旧方法的对比（验证修复的正确性）
        let test_negative = -121;
        let old_method = (test_negative - tick_spacing + 1) / tick_spacing; // -3
        let new_method = test_negative.div_euclid(tick_spacing); // -3
        assert_eq!(old_method, new_method); // 这个特定情况下结果相同
        
        // 但在其他情况下会有差异
        let test_negative2 = -119;
        let old_method2 = (test_negative2 - tick_spacing + 1) / tick_spacing; // -2
        let new_method2 = test_negative2.div_euclid(tick_spacing); // -2
        assert_eq!(old_method2, new_method2);
        
        // 更重要的是，div_euclid确保一致的行为
        assert_eq!((-1_i32).div_euclid(60), -1);
        assert_eq!((-59_i32).div_euclid(60), -1);
        assert_eq!((-60_i32).div_euclid(60), -1);
    }
}
