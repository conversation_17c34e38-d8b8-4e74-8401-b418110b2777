//! # Pool Registry
//!
//! 池地址注册表模块，提供流动性池地址的管理和查询功能。
//!
//! ## 功能特性
//!
//! - 池地址的增删查改操作
//! - 多重索引查询（按代币对、DEX协议、优先级查询）
//! - 持久化功能（JSON配置文件）
//! - 统计功能
//! - 并发安全访问
//!
//! ## 使用示例
//!
//! ```rust
//! use pool_registry::{PoolRegistry, PoolSubscription, DexProtocol, PoolType, SubscriptionPriority};
//! use solana_sdk::pubkey::Pubkey;
//!
//! // 创建新的注册表
//! let registry = PoolRegistry::new();
//!
//! // 添加池地址
//! let pool = PoolSubscription {
//!     address: Pubkey::new_unique(),
//!     token_pair: Default::default(),
//!     dex_protocol: DexProtocol::Raydium,
//!     pool_type: PoolType::Clmm,
//!     priority: SubscriptionPriority::High,
//!     is_active: true,
//!     metadata: Default::default(),
//! };
//!
//! registry.add_pool(pool);
//! ```

pub mod error;
pub mod registry;
pub mod types;

pub use error::PoolRegistryError;
pub use registry::PoolRegistry;
pub use types::{
    DexProtocol, PoolMetadata, PoolSubscription, PoolType, RegistryStats, SubscriptionPriority,
    TokenPair,
};
