use shared::EchoesError;
use thiserror::Error;

/// Pool Registry 专用错误类型
#[derive(Error, Debug)]
pub enum PoolRegistryError {
    #[error("Pool already exists: {0}")]
    PoolExists(String),

    #[error("Pool not found: {0}")]
    PoolNotFound(String),

    #[error("Invalid pool configuration: {0}")]
    InvalidPool(String),

    #[error("Serialization error: {0}")]
    Serialization(String),

    #[error("File I/O error: {0}")]
    FileIo(String),

    #[error("JSON parsing error: {0}")]
    JsonParsing(String),

    #[error("Lock error: {0}")]
    Lock(String),

    #[error("Shared error: {0}")]
    Shared(#[from] EchoesError),
}

impl From<serde_json::Error> for PoolRegistryError {
    fn from(err: serde_json::Error) -> Self {
        PoolRegistryError::JsonParsing(err.to_string())
    }
}

impl From<tokio::io::Error> for PoolRegistryError {
    fn from(err: tokio::io::Error) -> Self {
        PoolRegistryError::FileIo(err.to_string())
    }
}

/// Pool Registry 结果类型
pub type PoolRegistryResult<T> = Result<T, PoolRegistryError>;
