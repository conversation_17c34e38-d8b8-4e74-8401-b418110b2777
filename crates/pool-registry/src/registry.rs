use crate::error::{PoolRegistryError, PoolRegistryResult};
use crate::types::{DexProtocol, PoolSubscription, RegistryStats, SubscriptionPriority, TokenPair};
use solana_sdk::pubkey::Pubkey;
use std::collections::{HashMap, HashSet};
use std::path::Path;
use std::sync::Arc;
use tokio::sync::RwLock;
use tracing::{debug, error, info, warn};

/// 主要的池注册表实现
///
/// 提供线程安全的池地址管理，支持多重索引查询和持久化功能。
///
/// ## 并发安全
///
/// 使用 `Arc<RwLock<>>` 实现并发安全，支持多个读者或单个写者的访问模式。
///
/// ## 索引结构
///
/// - 主索引：地址 -> 池信息
/// - 代币对索引：代币对 -> 池地址集合
/// - 协议索引：DEX协议 -> 池地址集合  
/// - 优先级索引：优先级 -> 池地址集合
#[derive(Debug)]
pub struct PoolRegistry {
    /// 主要的池存储：地址 -> 池订阅信息
    pools: Arc<RwLock<HashMap<Pubkey, PoolSubscription>>>,
    /// 代币对索引：代币对 -> 池地址集合
    token_pair_index: Arc<RwLock<HashMap<TokenPair, HashSet<Pubkey>>>>,
    /// 协议索引：DEX协议 -> 池地址集合
    protocol_index: Arc<RwLock<HashMap<DexProtocol, HashSet<Pubkey>>>>,
    /// 优先级索引：优先级 -> 池地址集合
    priority_index: Arc<RwLock<HashMap<SubscriptionPriority, HashSet<Pubkey>>>>,
}

impl Default for PoolRegistry {
    fn default() -> Self {
        Self::new()
    }
}

impl PoolRegistry {
    /// 创建新的池注册表实例
    pub fn new() -> Self {
        debug!("Creating new PoolRegistry instance");
        Self {
            pools: Arc::new(RwLock::new(HashMap::new())),
            token_pair_index: Arc::new(RwLock::new(HashMap::new())),
            protocol_index: Arc::new(RwLock::new(HashMap::new())),
            priority_index: Arc::new(RwLock::new(HashMap::new())),
        }
    }

    /// 添加池到注册表
    ///
    /// 如果池已存在，返回错误。要更新现有池，请使用 `update_pool`。
    pub async fn add_pool(&self, mut pool: PoolSubscription) -> PoolRegistryResult<()> {
        let address = pool.address;
        debug!("Adding pool: {}", address);

        // 更新时间戳
        pool.touch();

        let mut pools = self.pools.write().await;

        // 检查是否已存在
        if pools.contains_key(&address) {
            warn!("Attempted to add existing pool: {}", address);
            return Err(PoolRegistryError::PoolExists(address.to_string()));
        }

        // 更新索引
        self.update_indices_on_add(&pool).await;

        // 添加到主存储
        pools.insert(address, pool);

        info!("Successfully added pool: {}", address);
        Ok(())
    }

    /// 移除池从注册表
    pub async fn remove_pool(&self, address: &Pubkey) -> PoolRegistryResult<PoolSubscription> {
        debug!("Removing pool: {}", address);

        let mut pools = self.pools.write().await;

        match pools.remove(address) {
            Some(pool) => {
                // 更新索引
                self.update_indices_on_remove(&pool).await;
                info!("Successfully removed pool: {}", address);
                Ok(pool)
            }
            None => {
                warn!("Attempted to remove non-existent pool: {}", address);
                Err(PoolRegistryError::PoolNotFound(address.to_string()))
            }
        }
    }

    /// 获取池信息
    pub async fn get_pool(&self, address: &Pubkey) -> Option<PoolSubscription> {
        debug!("Getting pool: {}", address);
        let pools = self.pools.read().await;
        pools.get(address).cloned()
    }

    /// 更新池信息
    ///
    /// 如果池不存在，返回错误。要添加新池，请使用 `add_pool`。
    pub async fn update_pool(&self, mut pool: PoolSubscription) -> PoolRegistryResult<()> {
        let address = pool.address;
        debug!("Updating pool: {}", address);

        // 更新时间戳
        pool.touch();

        let mut pools = self.pools.write().await;

        match pools.get(&address) {
            Some(old_pool) => {
                // 如果索引键改变了，需要更新索引
                if self.needs_index_update(old_pool, &pool) {
                    self.update_indices_on_remove(old_pool).await;
                    self.update_indices_on_add(&pool).await;
                }

                pools.insert(address, pool);
                info!("Successfully updated pool: {}", address);
                Ok(())
            }
            None => {
                warn!("Attempted to update non-existent pool: {}", address);
                Err(PoolRegistryError::PoolNotFound(address.to_string()))
            }
        }
    }

    /// 按代币对查询池
    pub async fn get_pools_by_token_pair(&self, token_pair: &TokenPair) -> Vec<PoolSubscription> {
        debug!("Getting pools by token pair: {}", token_pair);

        let token_pair_index = self.token_pair_index.read().await;
        let pools = self.pools.read().await;

        token_pair_index
            .get(token_pair)
            .map(|addresses| {
                addresses
                    .iter()
                    .filter_map(|addr| pools.get(addr).cloned())
                    .collect()
            })
            .unwrap_or_default()
    }

    /// 按代币查询池（包含指定代币的所有池）
    pub async fn get_pools_by_token(&self, token: &Pubkey) -> Vec<PoolSubscription> {
        debug!("Getting pools by token: {}", token);

        let token_pair_index = self.token_pair_index.read().await;
        let pools = self.pools.read().await;

        let mut result = Vec::new();

        for (pair, addresses) in token_pair_index.iter() {
            if pair.contains(token) {
                for addr in addresses {
                    if let Some(pool) = pools.get(addr) {
                        result.push(pool.clone());
                    }
                }
            }
        }

        result
    }

    /// 按 DEX 协议查询池
    pub async fn get_pools_by_protocol(&self, protocol: DexProtocol) -> Vec<PoolSubscription> {
        debug!("Getting pools by protocol: {}", protocol);

        let protocol_index = self.protocol_index.read().await;
        let pools = self.pools.read().await;

        protocol_index
            .get(&protocol)
            .map(|addresses| {
                addresses
                    .iter()
                    .filter_map(|addr| pools.get(addr).cloned())
                    .collect()
            })
            .unwrap_or_default()
    }

    /// 按优先级查询池
    pub async fn get_pools_by_priority(
        &self,
        priority: SubscriptionPriority,
    ) -> Vec<PoolSubscription> {
        debug!("Getting pools by priority: {}", priority);

        let priority_index = self.priority_index.read().await;
        let pools = self.pools.read().await;

        priority_index
            .get(&priority)
            .map(|addresses| {
                addresses
                    .iter()
                    .filter_map(|addr| pools.get(addr).cloned())
                    .collect()
            })
            .unwrap_or_default()
    }

    /// 按优先级排序获取所有激活的池
    pub async fn get_active_pools_by_priority(&self) -> Vec<PoolSubscription> {
        debug!("Getting active pools sorted by priority");

        let pools = self.pools.read().await;

        let mut active_pools: Vec<_> = pools
            .values()
            .filter(|pool| pool.is_active)
            .cloned()
            .collect();

        // 按优先级排序（高优先级在前）
        active_pools.sort_by(|a, b| b.priority.cmp(&a.priority));

        active_pools
    }

    /// 获取所有池
    pub async fn get_all_pools(&self) -> Vec<PoolSubscription> {
        debug!("Getting all pools");
        let pools = self.pools.read().await;
        pools.values().cloned().collect()
    }

    /// 获取注册表统计信息
    pub async fn get_stats(&self) -> RegistryStats {
        debug!("Getting registry statistics");

        let pools = self.pools.read().await;
        let token_pair_index = self.token_pair_index.read().await;

        let mut stats = RegistryStats::default();
        stats.total_pools = pools.len();
        stats.unique_token_pairs = token_pair_index.len();

        for pool in pools.values() {
            if pool.is_active {
                stats.active_pools += 1;
            }

            // 统计协议分布
            let protocol_key = pool.dex_protocol.to_string();
            *stats.pools_by_protocol.entry(protocol_key).or_insert(0) += 1;

            // 统计类型分布
            let type_key = pool.pool_type.to_string();
            *stats.pools_by_type.entry(type_key).or_insert(0) += 1;

            // 统计优先级分布
            let priority_key = pool.priority.to_string();
            *stats.pools_by_priority.entry(priority_key).or_insert(0) += 1;
        }

        stats
    }

    /// 从 JSON 文件加载池数据
    pub async fn load_from_file<P: AsRef<Path>>(&self, path: P) -> PoolRegistryResult<usize> {
        let path = path.as_ref();
        info!("Loading pools from file: {:?}", path);

        let content = tokio::fs::read_to_string(path).await?;
        let pools: Vec<PoolSubscription> = serde_json::from_str(&content)?;

        let mut loaded_count = 0;
        for pool in pools {
            match self.add_pool(pool).await {
                Ok(()) => loaded_count += 1,
                Err(PoolRegistryError::PoolExists(_)) => {
                    // 池已存在，跳过
                    continue;
                }
                Err(e) => {
                    error!("Failed to load pool: {}", e);
                    return Err(e);
                }
            }
        }

        info!("Successfully loaded {} pools from file", loaded_count);
        Ok(loaded_count)
    }

    /// 保存池数据到 JSON 文件
    pub async fn save_to_file<P: AsRef<Path>>(&self, path: P) -> PoolRegistryResult<usize> {
        let path = path.as_ref();
        info!("Saving pools to file: {:?}", path);

        let pools = self.get_all_pools().await;
        let content = serde_json::to_string_pretty(&pools)?;

        tokio::fs::write(path, content).await?;

        info!("Successfully saved {} pools to file", pools.len());
        Ok(pools.len())
    }

    /// 清空所有池数据
    pub async fn clear(&self) -> PoolRegistryResult<()> {
        info!("Clearing all pools from registry");

        let mut pools = self.pools.write().await;
        let mut token_pair_index = self.token_pair_index.write().await;
        let mut protocol_index = self.protocol_index.write().await;
        let mut priority_index = self.priority_index.write().await;

        pools.clear();
        token_pair_index.clear();
        protocol_index.clear();
        priority_index.clear();

        info!("Successfully cleared all pools");
        Ok(())
    }

    /// 检查池是否存在
    pub async fn contains(&self, address: &Pubkey) -> bool {
        let pools = self.pools.read().await;
        pools.contains_key(address)
    }

    /// 获取池总数
    pub async fn len(&self) -> usize {
        let pools = self.pools.read().await;
        pools.len()
    }

    /// 检查是否为空
    pub async fn is_empty(&self) -> bool {
        let pools = self.pools.read().await;
        pools.is_empty()
    }

    // 私有辅助方法

    /// 添加池时更新索引
    async fn update_indices_on_add(&self, pool: &PoolSubscription) {
        // 更新代币对索引
        {
            let mut token_pair_index = self.token_pair_index.write().await;
            token_pair_index
                .entry(pool.token_pair.clone())
                .or_insert_with(HashSet::new)
                .insert(pool.address);
        }

        // 更新协议索引
        {
            let mut protocol_index = self.protocol_index.write().await;
            protocol_index
                .entry(pool.dex_protocol)
                .or_insert_with(HashSet::new)
                .insert(pool.address);
        }

        // 更新优先级索引
        {
            let mut priority_index = self.priority_index.write().await;
            priority_index
                .entry(pool.priority)
                .or_insert_with(HashSet::new)
                .insert(pool.address);
        }
    }

    /// 移除池时更新索引
    async fn update_indices_on_remove(&self, pool: &PoolSubscription) {
        // 更新代币对索引
        {
            let mut token_pair_index = self.token_pair_index.write().await;
            if let Some(addresses) = token_pair_index.get_mut(&pool.token_pair) {
                addresses.remove(&pool.address);
                if addresses.is_empty() {
                    token_pair_index.remove(&pool.token_pair);
                }
            }
        }

        // 更新协议索引
        {
            let mut protocol_index = self.protocol_index.write().await;
            if let Some(addresses) = protocol_index.get_mut(&pool.dex_protocol) {
                addresses.remove(&pool.address);
                if addresses.is_empty() {
                    protocol_index.remove(&pool.dex_protocol);
                }
            }
        }

        // 更新优先级索引
        {
            let mut priority_index = self.priority_index.write().await;
            if let Some(addresses) = priority_index.get_mut(&pool.priority) {
                addresses.remove(&pool.address);
                if addresses.is_empty() {
                    priority_index.remove(&pool.priority);
                }
            }
        }
    }

    /// 检查是否需要更新索引
    fn needs_index_update(&self, old_pool: &PoolSubscription, new_pool: &PoolSubscription) -> bool {
        old_pool.token_pair != new_pool.token_pair
            || old_pool.dex_protocol != new_pool.dex_protocol
            || old_pool.priority != new_pool.priority
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::types::{PoolType, SubscriptionPriority};
    use tempfile::tempdir;

    async fn create_test_pool(
        address: Pubkey,
        token_a: Pubkey,
        token_b: Pubkey,
        protocol: DexProtocol,
        priority: SubscriptionPriority,
    ) -> PoolSubscription {
        PoolSubscription::new(
            address,
            token_a,
            token_b,
            protocol,
            PoolType::Clmm,
            priority,
        )
    }

    #[tokio::test]
    async fn test_add_and_get_pool() {
        let registry = PoolRegistry::new();
        let address = Pubkey::new_unique();
        let token_a = Pubkey::new_unique();
        let token_b = Pubkey::new_unique();

        let pool = create_test_pool(
            address,
            token_a,
            token_b,
            DexProtocol::Raydium,
            SubscriptionPriority::High,
        )
        .await;

        // 添加池
        registry.add_pool(pool.clone()).await.unwrap();

        // 获取池
        let retrieved = registry.get_pool(&address).await.unwrap();
        assert_eq!(retrieved.address, address);
        assert_eq!(retrieved.dex_protocol, DexProtocol::Raydium);
        assert_eq!(retrieved.priority, SubscriptionPriority::High);
    }

    #[tokio::test]
    async fn test_add_duplicate_pool() {
        let registry = PoolRegistry::new();
        let address = Pubkey::new_unique();
        let token_a = Pubkey::new_unique();
        let token_b = Pubkey::new_unique();

        let pool = create_test_pool(
            address,
            token_a,
            token_b,
            DexProtocol::Raydium,
            SubscriptionPriority::High,
        )
        .await;

        // 第一次添加应该成功
        registry.add_pool(pool.clone()).await.unwrap();

        // 第二次添加应该失败
        let result = registry.add_pool(pool).await;
        assert!(matches!(result, Err(PoolRegistryError::PoolExists(_))));
    }

    #[tokio::test]
    async fn test_remove_pool() {
        let registry = PoolRegistry::new();
        let address = Pubkey::new_unique();
        let token_a = Pubkey::new_unique();
        let token_b = Pubkey::new_unique();

        let pool = create_test_pool(
            address,
            token_a,
            token_b,
            DexProtocol::Raydium,
            SubscriptionPriority::High,
        )
        .await;

        // 添加然后移除
        registry.add_pool(pool).await.unwrap();
        let removed = registry.remove_pool(&address).await.unwrap();
        assert_eq!(removed.address, address);

        // 确认池已被移除
        assert!(registry.get_pool(&address).await.is_none());
    }

    #[tokio::test]
    async fn test_query_by_token_pair() {
        let registry = PoolRegistry::new();
        let token_a = Pubkey::new_unique();
        let token_b = Pubkey::new_unique();
        let token_c = Pubkey::new_unique();

        let pool1 = create_test_pool(
            Pubkey::new_unique(),
            token_a,
            token_b,
            DexProtocol::Raydium,
            SubscriptionPriority::High,
        )
        .await;

        let pool2 = create_test_pool(
            Pubkey::new_unique(),
            token_a,
            token_c,
            DexProtocol::Meteora,
            SubscriptionPriority::Medium,
        )
        .await;

        registry.add_pool(pool1).await.unwrap();
        registry.add_pool(pool2).await.unwrap();

        // 查询包含 token_a 和 token_b 的池
        let token_pair = TokenPair::new(token_a, token_b);
        let pools = registry.get_pools_by_token_pair(&token_pair).await;
        assert_eq!(pools.len(), 1);
        assert_eq!(pools[0].dex_protocol, DexProtocol::Raydium);
    }

    #[tokio::test]
    async fn test_query_by_token() {
        let registry = PoolRegistry::new();
        let token_a = Pubkey::new_unique();
        let token_b = Pubkey::new_unique();
        let token_c = Pubkey::new_unique();

        let pool1 = create_test_pool(
            Pubkey::new_unique(),
            token_a,
            token_b,
            DexProtocol::Raydium,
            SubscriptionPriority::High,
        )
        .await;

        let pool2 = create_test_pool(
            Pubkey::new_unique(),
            token_a,
            token_c,
            DexProtocol::Meteora,
            SubscriptionPriority::Medium,
        )
        .await;

        registry.add_pool(pool1).await.unwrap();
        registry.add_pool(pool2).await.unwrap();

        // 查询包含 token_a 的所有池
        let pools = registry.get_pools_by_token(&token_a).await;
        assert_eq!(pools.len(), 2);
    }

    #[tokio::test]
    async fn test_query_by_protocol() {
        let registry = PoolRegistry::new();

        let pool1 = create_test_pool(
            Pubkey::new_unique(),
            Pubkey::new_unique(),
            Pubkey::new_unique(),
            DexProtocol::Raydium,
            SubscriptionPriority::High,
        )
        .await;

        let pool2 = create_test_pool(
            Pubkey::new_unique(),
            Pubkey::new_unique(),
            Pubkey::new_unique(),
            DexProtocol::Meteora,
            SubscriptionPriority::Medium,
        )
        .await;

        registry.add_pool(pool1).await.unwrap();
        registry.add_pool(pool2).await.unwrap();

        let raydium_pools = registry.get_pools_by_protocol(DexProtocol::Raydium).await;
        assert_eq!(raydium_pools.len(), 1);

        let meteora_pools = registry.get_pools_by_protocol(DexProtocol::Meteora).await;
        assert_eq!(meteora_pools.len(), 1);
    }

    #[tokio::test]
    async fn test_active_pools_by_priority() {
        let registry = PoolRegistry::new();

        let mut pool1 = create_test_pool(
            Pubkey::new_unique(),
            Pubkey::new_unique(),
            Pubkey::new_unique(),
            DexProtocol::Raydium,
            SubscriptionPriority::High,
        )
        .await;

        let pool2 = create_test_pool(
            Pubkey::new_unique(),
            Pubkey::new_unique(),
            Pubkey::new_unique(),
            DexProtocol::Meteora,
            SubscriptionPriority::Critical,
        )
        .await;

        let mut pool3 = create_test_pool(
            Pubkey::new_unique(),
            Pubkey::new_unique(),
            Pubkey::new_unique(),
            DexProtocol::Orca,
            SubscriptionPriority::Low,
        )
        .await;

        // 设置一个池为非激活状态
        pool1.is_active = false;
        pool3.is_active = false;

        registry.add_pool(pool1).await.unwrap();
        registry.add_pool(pool2).await.unwrap();
        registry.add_pool(pool3).await.unwrap();

        let active_pools = registry.get_active_pools_by_priority().await;
        // 只有一个激活的池
        assert_eq!(active_pools.len(), 1);
        assert_eq!(active_pools[0].priority, SubscriptionPriority::Critical);
    }

    #[tokio::test]
    async fn test_statistics() {
        let registry = PoolRegistry::new();

        let pool1 = create_test_pool(
            Pubkey::new_unique(),
            Pubkey::new_unique(),
            Pubkey::new_unique(),
            DexProtocol::Raydium,
            SubscriptionPriority::High,
        )
        .await;

        let pool2 = create_test_pool(
            Pubkey::new_unique(),
            Pubkey::new_unique(),
            Pubkey::new_unique(),
            DexProtocol::Meteora,
            SubscriptionPriority::High,
        )
        .await;

        registry.add_pool(pool1).await.unwrap();
        registry.add_pool(pool2).await.unwrap();

        let stats = registry.get_stats().await;
        assert_eq!(stats.total_pools, 2);
        assert_eq!(stats.active_pools, 2);
        assert_eq!(stats.unique_token_pairs, 2);
        assert_eq!(stats.pools_by_protocol.len(), 2);
    }

    #[tokio::test]
    async fn test_persistence() {
        let registry = PoolRegistry::new();
        let temp_dir = tempdir().unwrap();
        let file_path = temp_dir.path().join("test_pools.json");

        let pool = create_test_pool(
            Pubkey::new_unique(),
            Pubkey::new_unique(),
            Pubkey::new_unique(),
            DexProtocol::Raydium,
            SubscriptionPriority::High,
        )
        .await;

        registry.add_pool(pool.clone()).await.unwrap();

        // 保存到文件
        let saved_count = registry.save_to_file(&file_path).await.unwrap();
        assert_eq!(saved_count, 1);

        // 清空注册表
        registry.clear().await.unwrap();
        assert!(registry.is_empty().await);

        // 从文件加载
        let loaded_count = registry.load_from_file(&file_path).await.unwrap();
        assert_eq!(loaded_count, 1);

        // 验证数据
        let loaded_pool = registry.get_pool(&pool.address).await.unwrap();
        assert_eq!(loaded_pool.address, pool.address);
        assert_eq!(loaded_pool.dex_protocol, pool.dex_protocol);
    }
}
