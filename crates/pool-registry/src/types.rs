use serde::{Deserialize, Serialize};
use solana_sdk::pubkey::Pubkey;
use std::collections::HashMap;

/// DEX 协议类型
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum DexProtocol {
    /// Raydium DEX
    Raydium,
    /// Meteora DEX
    Meteora,
    /// Orca DEX
    Orca,
    /// Jupiter DEX
    Jupiter,
    /// Phoenix DEX
    Phoenix,
    /// 其他协议
    Other(u8),
}

impl std::fmt::Display for DexProtocol {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            DexProtocol::Raydium => write!(f, "Raydium"),
            DexProtocol::Meteora => write!(f, "Meteora"),
            DexProtocol::Orca => write!(f, "Orca"),
            DexProtocol::Jupiter => write!(f, "Jupiter"),
            DexProtocol::Phoenix => write!(f, "Phoenix"),
            DexProtocol::Other(id) => write!(f, "Other({})", id),
        }
    }
}

/// 池类型
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum PoolType {
    /// 集中流动性做市商模型 (Concentrated Liquidity Market Maker)
    Clmm,
    /// 动态流动性做市商模型 (Dynamic Liquidity Market Maker)
    Dlmm,
    /// 自动做市商模型 (Automated Market Maker)
    Amm,
    /// 稳定币池
    Stable,
    /// 其他类型
    Other(String),
}

impl std::fmt::Display for PoolType {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            PoolType::Clmm => write!(f, "CLMM"),
            PoolType::Dlmm => write!(f, "DLMM"),
            PoolType::Amm => write!(f, "AMM"),
            PoolType::Stable => write!(f, "Stable"),
            PoolType::Other(name) => write!(f, "{}", name),
        }
    }
}

/// 订阅优先级
#[derive(Debug, Clone, Copy, PartialEq, Eq, PartialOrd, Ord, Hash, Serialize, Deserialize)]
pub enum SubscriptionPriority {
    /// 低优先级 - 优先级值: 1
    Low = 1,
    /// 中等优先级 - 优先级值: 2  
    Medium = 2,
    /// 高优先级 - 优先级值: 3
    High = 3,
    /// 关键优先级 - 优先级值: 4
    Critical = 4,
}

impl std::fmt::Display for SubscriptionPriority {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            SubscriptionPriority::Low => write!(f, "Low"),
            SubscriptionPriority::Medium => write!(f, "Medium"),
            SubscriptionPriority::High => write!(f, "High"),
            SubscriptionPriority::Critical => write!(f, "Critical"),
        }
    }
}

/// 代币对，确保较小的 pubkey 在前面
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub struct TokenPair {
    pub token_a: Pubkey,
    pub token_b: Pubkey,
}

impl TokenPair {
    /// 创建新的代币对，自动确保较小的 pubkey 在前面
    pub fn new(token_a: Pubkey, token_b: Pubkey) -> Self {
        if token_a <= token_b {
            Self { token_a, token_b }
        } else {
            Self {
                token_a: token_b,
                token_b: token_a,
            }
        }
    }

    /// 检查代币对是否包含指定的代币
    pub fn contains(&self, token: &Pubkey) -> bool {
        &self.token_a == token || &self.token_b == token
    }

    /// 获取另一个代币，如果传入的代币在代币对中
    pub fn get_other_token(&self, token: &Pubkey) -> Option<Pubkey> {
        if &self.token_a == token {
            Some(self.token_b)
        } else if &self.token_b == token {
            Some(self.token_a)
        } else {
            None
        }
    }
}

impl Default for TokenPair {
    fn default() -> Self {
        Self::new(Pubkey::default(), Pubkey::default())
    }
}

impl std::fmt::Display for TokenPair {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}/{}", self.token_a, self.token_b)
    }
}

/// 池元数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PoolMetadata {
    /// 池名称
    pub name: Option<String>,
    /// 池描述
    pub description: Option<String>,
    /// 手续费率 (basis points)
    pub fee_rate: Option<u64>,
    /// 创建时间戳
    pub created_at: Option<i64>,
    /// 最后更新时间戳
    pub updated_at: Option<i64>,
    /// 扩展属性
    pub attributes: HashMap<String, String>,
}

impl Default for PoolMetadata {
    fn default() -> Self {
        Self {
            name: None,
            description: None,
            fee_rate: None,
            created_at: None,
            updated_at: None,
            attributes: HashMap::new(),
        }
    }
}

/// 池订阅信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PoolSubscription {
    /// 池地址
    pub address: Pubkey,
    /// 代币对
    pub token_pair: TokenPair,
    /// DEX 协议
    pub dex_protocol: DexProtocol,
    /// 池类型
    pub pool_type: PoolType,
    /// 订阅优先级
    pub priority: SubscriptionPriority,
    /// 是否激活
    pub is_active: bool,
    /// 池元数据
    pub metadata: PoolMetadata,
}

impl PoolSubscription {
    /// 创建新的池订阅
    pub fn new(
        address: Pubkey,
        token_a: Pubkey,
        token_b: Pubkey,
        dex_protocol: DexProtocol,
        pool_type: PoolType,
        priority: SubscriptionPriority,
    ) -> Self {
        Self {
            address,
            token_pair: TokenPair::new(token_a, token_b),
            dex_protocol,
            pool_type,
            priority,
            is_active: true,
            metadata: PoolMetadata::default(),
        }
    }

    /// 设置池元数据
    pub fn with_metadata(mut self, metadata: PoolMetadata) -> Self {
        self.metadata = metadata;
        self
    }

    /// 设置激活状态
    pub fn with_active(mut self, is_active: bool) -> Self {
        self.is_active = is_active;
        self
    }

    /// 更新元数据中的时间戳
    pub fn touch(&mut self) {
        let now = chrono::Utc::now().timestamp();
        if self.metadata.created_at.is_none() {
            self.metadata.created_at = Some(now);
        }
        self.metadata.updated_at = Some(now);
    }
}

/// 注册表统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RegistryStats {
    /// 总池数量
    pub total_pools: usize,
    /// 激活池数量
    pub active_pools: usize,
    /// 按协议分组的池数量
    pub pools_by_protocol: HashMap<String, usize>,
    /// 按类型分组的池数量
    pub pools_by_type: HashMap<String, usize>,
    /// 按优先级分组的池数量
    pub pools_by_priority: HashMap<String, usize>,
    /// 代币对数量
    pub unique_token_pairs: usize,
}

impl Default for RegistryStats {
    fn default() -> Self {
        Self {
            total_pools: 0,
            active_pools: 0,
            pools_by_protocol: HashMap::new(),
            pools_by_type: HashMap::new(),
            pools_by_priority: HashMap::new(),
            unique_token_pairs: 0,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_token_pair_ordering() {
        let pubkey_a = Pubkey::new_unique();
        let pubkey_b = Pubkey::new_unique();

        let pair1 = TokenPair::new(pubkey_a, pubkey_b);
        let pair2 = TokenPair::new(pubkey_b, pubkey_a);

        // 无论顺序如何，结果应该相同
        assert_eq!(pair1, pair2);

        // 较小的 pubkey 应该在前面
        if pubkey_a < pubkey_b {
            assert_eq!(pair1.token_a, pubkey_a);
            assert_eq!(pair1.token_b, pubkey_b);
        } else {
            assert_eq!(pair1.token_a, pubkey_b);
            assert_eq!(pair1.token_b, pubkey_a);
        }
    }

    #[test]
    fn test_token_pair_contains() {
        let pubkey_a = Pubkey::new_unique();
        let pubkey_b = Pubkey::new_unique();
        let pubkey_c = Pubkey::new_unique();

        let pair = TokenPair::new(pubkey_a, pubkey_b);

        assert!(pair.contains(&pubkey_a));
        assert!(pair.contains(&pubkey_b));
        assert!(!pair.contains(&pubkey_c));
    }

    #[test]
    fn test_token_pair_get_other() {
        let pubkey_a = Pubkey::new_unique();
        let pubkey_b = Pubkey::new_unique();
        let pubkey_c = Pubkey::new_unique();

        let pair = TokenPair::new(pubkey_a, pubkey_b);

        assert_eq!(pair.get_other_token(&pubkey_a), Some(pubkey_b));
        assert_eq!(pair.get_other_token(&pubkey_b), Some(pubkey_a));
        assert_eq!(pair.get_other_token(&pubkey_c), None);
    }

    #[test]
    fn test_subscription_priority_ordering() {
        assert!(SubscriptionPriority::Critical > SubscriptionPriority::High);
        assert!(SubscriptionPriority::High > SubscriptionPriority::Medium);
        assert!(SubscriptionPriority::Medium > SubscriptionPriority::Low);
    }

    #[test]
    fn test_pool_subscription_creation() {
        let address = Pubkey::new_unique();
        let token_a = Pubkey::new_unique();
        let token_b = Pubkey::new_unique();

        let subscription = PoolSubscription::new(
            address,
            token_a,
            token_b,
            DexProtocol::Raydium,
            PoolType::Clmm,
            SubscriptionPriority::High,
        );

        assert_eq!(subscription.address, address);
        assert_eq!(subscription.token_pair, TokenPair::new(token_a, token_b));
        assert_eq!(subscription.dex_protocol, DexProtocol::Raydium);
        assert_eq!(subscription.pool_type, PoolType::Clmm);
        assert_eq!(subscription.priority, SubscriptionPriority::High);
        assert!(subscription.is_active);
    }

    #[test]
    fn test_pool_subscription_touch() {
        let mut subscription = PoolSubscription::new(
            Pubkey::new_unique(),
            Pubkey::new_unique(),
            Pubkey::new_unique(),
            DexProtocol::Raydium,
            PoolType::Clmm,
            SubscriptionPriority::High,
        );

        assert!(subscription.metadata.created_at.is_none());
        assert!(subscription.metadata.updated_at.is_none());

        subscription.touch();

        assert!(subscription.metadata.created_at.is_some());
        assert!(subscription.metadata.updated_at.is_some());

        let created_at = subscription.metadata.created_at;

        // 再次调用 touch，created_at 不应该变化
        subscription.touch();
        assert_eq!(subscription.metadata.created_at, created_at);
        assert!(subscription.metadata.updated_at.is_some());
    }
}
