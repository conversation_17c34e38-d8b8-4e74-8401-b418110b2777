use std::sync::Arc;
use tokio::sync::mpsc;
use tracing::info;
use chain_listener::{ChainListenerConfig, DataHandler, SubscriptionConfig, YellowstoneGrpcClient};
use data_parser::{global_registry, register_global_parser, AccountParserRegistry, AccountType, DexEvent, RaydiumClmmEventParser};
use data_parser::accounts::{MeteoraAnchorAccountParser, RaydiumAnchorAccountParser};
use shared::{DataType};
use crate::logging::init_logging;

mod logging;


// vec![
//     "CAMMCzo5YL8w4VFF8KVHrK22GGUsp5VTaW7grrKgrWqK".to_string(), // Raydium CLMM
// ]

#[tokio::main]
async fn main() {
    let _guard = init_logging();
    info!("🚀🚀🚀 Starting application");

    // 注册所有解析器
    setup_parsers().await;

    // 创建事件收集器
    let (event_tx, mut event_rx) = mpsc::unbounded_channel::<Box<dyn DexEvent>>();

    // 配置并启动链监听器
    let config = ChainListenerConfig::default();
    let client = YellowstoneGrpcClient::new(config);

    let subscription_config = SubscriptionConfig {
        accounts: Some(
            vec![
                "3ucNos4NbumPLZNWztqGHNFFgkHeRMBQAVemeeomsUxv".to_string(),
                "5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6".to_string()
            ]
        ),
        vote: false,
        failed: false,
        ..Default::default()
    };

    let event_tx_clone = event_tx.clone();
    let data_handler: DataHandler = |data: DataType| -> Result<(), chain_listener::ChainListenerError> {

        match data {
            DataType::Transaction(tx) => {
                // 解析交易数据
                let slot = tx.slot;
                let signature = tx.signature.to_string();
                let bot_wallet = None;
                let parser = RaydiumClmmEventParser::new();


                let signature_clone = signature.clone();
                let bot_wallet_clone = bot_wallet.clone();

                tokio::spawn(async move {
                    let parsers = global_registry().get_all_parsers();
                    for parse in parsers {
                        let tx_clone = tx.tx.clone();
                        let res = parse.parse_transaction(
                            tx_clone,
                            &signature_clone,
                            Some(slot),
                            None,
                            bot_wallet_clone,
                        ).await.unwrap_or_else(|_e| vec![]);
                        println!("res: {:?}", res);
                    }
                });
            }
            DataType::Account(acc) => {
                // 解析账号数据

                tokio::spawn(async move {
                    let meteora_account_parse = MeteoraAnchorAccountParser::new();
                    let raydium_account_parse = RaydiumAnchorAccountParser::new();

                    let account_parser = AccountParserRegistry::new();
                    account_parser.register_parser(Arc::new(meteora_account_parse))
                        .expect("Failed to register Meteora parser");
                    account_parser.register_parser(Arc::new(raydium_account_parse))
                        .expect("Failed to register Raydium parser");


                    let meteora = "LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo";
                    let raydium = "CAMMCzo5YL8w4VFF8KVHrK22GGUsp5VTaW7grrKgrWqK";

                    if acc.pubkey.to_string() == "3ucNos4NbumPLZNWztqGHNFFgkHeRMBQAVemeeomsUxv".to_string() {
                        println!("Parsing Raydium PoolState for account: {:?}", acc.data);
                        let parsers = account_parser.find_parsers_for_account_type(&AccountType::RaydiumPoolState);
                        for parser in parsers {
                            println!("raydium parsers: {:?}", parser.name());
                            let res = parser.parse_account(
                                acc.pubkey.parse().unwrap(),
                                raydium.parse().unwrap(),
                                &acc.data).await;
                            match res {
                                Ok(parsed_data) => {
                                    println!("Parsed Raydium PoolState: {:?}", parsed_data);
                                }
                                Err(e) => {
                                    println!("Error parsing Raydium PoolState: {:?}", e);
                                }
                            }
                        }
                    }

                    if acc.pubkey.to_string() == "5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6".to_string() {
                        println!("Parsing Meteora PoolState for account: {:?}", acc.data);
                        let parsers = account_parser.find_parsers_for_account_type(&AccountType::MeteoraLbPair);
                        for parser in parsers {
                            let res = parser.parse_account(
                                acc.pubkey.parse().unwrap(),
                                meteora.parse().unwrap(),
                                &acc.data).await;
                            match res {
                                Ok(parsed_data) => {
                                    println!("Parsed Meteora PoolState: {:?}", parsed_data);
                                }
                                Err(e) => {
                                    println!("Error parsing Meteora PoolState: {:?}", e);
                                }
                            }
                        }
                    }
                });
            }
            DataType::Block(_) => {}
            DataType::Slot(_) => {}
        }
        Ok(())
    };

    let res = client.subscribe_with_config(subscription_config, data_handler).await;
    if let Err(e) = res {
        println!("Error establishing subscription: {:?}", e);
        return;
    }
    println!("Subscription established successfully");

}


async fn setup_parsers() {
    let raydium_parser = Arc::new(RaydiumClmmEventParser::new());
    register_global_parser(raydium_parser).expect("Failed to register Raydium parser");
}
