use thiserror::Error;

/// 系统级别的错误类型
#[derive(Error, Debug)]
pub enum EchoesError {
    #[error("Configuration error: {0}")]
    Config(String),
    
    #[error("Network error: {0}")]
    Network(String),
    
    #[error("Parsing error: {0}")]
    Parsing(String),
    
    #[error("Parse error: {0}")]
    Parse(String),
    
    #[error("Storage error: {0}")]
    Storage(String),
    
    #[error("Internal error: {0}")]
    Internal(String),
    
    #[error("Invalid state: {0}")]
    InvalidState(String),
    
    #[error("IO error: {0}")]
    Io(String),
}

/// 通用结果类型
pub type Result<T> = std::result::Result<T, EchoesError>;